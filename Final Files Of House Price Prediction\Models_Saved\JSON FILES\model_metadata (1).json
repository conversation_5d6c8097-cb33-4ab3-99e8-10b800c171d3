{"project_name": "House Price Prediction - Complete Analysis", "model_version": "v1.0_20250605_083727", "creation_date": "2025-06-05T08:37:27.663505", "models_trained": ["LinearRegression", "RandomForest", "GradientBoosting", "XGBoost"], "best_model": "GradientBoosting", "best_model_r2": 0.9107613462991371, "training_data_shape": [1168, 15], "test_data_shape": [292, 15], "features_count": 15, "model_files": {"LinearRegression": {"joblib_file": "saved_models/linearregression_model.joblib", "pickle_file": "saved_models/linearregression_model.pkl", "joblib_size_mb": "0.00", "pickle_size_mb": "0.00", "save_timestamp": "20250605_083727"}, "RandomForest": {"joblib_file": "saved_models/randomforest_model.joblib", "pickle_file": "saved_models/randomforest_model.pkl", "joblib_size_mb": "8.76", "pickle_size_mb": "8.76", "save_timestamp": "20250605_083727"}, "GradientBoosting": {"joblib_file": "saved_models/gradientboosting_model.joblib", "pickle_file": "saved_models/gradientboosting_model.pkl", "joblib_size_mb": "0.66", "pickle_size_mb": "0.65", "save_timestamp": "20250605_083727"}, "XGBoost": {"joblib_file": "saved_models/xgboost_model.joblib", "pickle_file": "saved_models/xgboost_model.pkl", "joblib_size_mb": "0.32", "pickle_size_mb": "0.32", "save_timestamp": "20250605_083727"}}, "performance_summary": {"LinearRegression": {"R2": 0.8156671533283045, "RMSE": 37601.7694353778}, "RandomForest": {"R2": 0.8984239096222211, "RMSE": 27912.752426951483}, "GradientBoosting": {"R2": 0.9107613462991371, "RMSE": 26162.751846390795}, "XGBoost": {"R2": 0.8980016112327576, "RMSE": 27970.712683090504}}, "libraries_used": ["pandas", "numpy", "scikit-learn", "xgboost", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "seaborn"]}