{"nbformat": 4, "nbformat_minor": 0, "metadata": {"colab": {"provenance": []}, "kernelspec": {"name": "python3", "display_name": "Python 3"}, "language_info": {"name": "python"}}, "cells": [{"cell_type": "markdown", "source": ["# 🏠 PRCP-1020 House Price Prediction: Complete Comprehensive Analysis\n", "\n", "## 📋 **Project Overview**\n", "\n", "This comprehensive notebook contains the complete end-to-end house price prediction system, merging all components from the Enhanced Implementation into a single, well-organized analysis.\n", "\n", "### **🎯 Problem Statement & Tasks**\n", "\n", "**Task 1:** Prepare a complete data analysis report on the given data.\n", "\n", "**Task 2:** \n", "- a) Create a robust machine learning algorithm to accurately predict the price of the house given the various factors across the market.\n", "- b) Determine the relationship between the house features and how the price varies based on this.\n", "\n", "**Task 3:** Come up with suggestions for the customer to buy the house according to the area, price and other requirements.\n", "\n", "### **📊 Complete Analysis Structure**\n", "\n", "| **Section** | **Component** | **Description** | **Source** |\n", "|-------------|---------------|-----------------|------------|\n", "| **Part 1** | **Visualization & EDA** | Comprehensive exploratory data analysis with advanced visualizations | 01_Data_Analysis |\n", "| **Part 2** | **Data Preprocessing** | Data cleaning, feature engineering, and preparation | 01_Data_Analysis |\n", "| **Part 3** | **Model Building** | Advanced ML algorithms, ensemble methods, hyperparameter tuning | 02_Advanced_Modeling |\n", "| **Part 4** | **Model Interpretation** | SHAP analysis, feature importance, business insights | 04_Model_Interpretation |\n", "| **Part 5** | **Business Application** | Customer recommendation system, investment analysis | 03_Business_Application |\n", "| **Part 6** | **Complete Demo** | End-to-end system demonstration and deployment | 05_Complete_Demo |\n", "\n", "### **🚀 Key Features**\n", "- **Advanced EDA:** 13+ analysis sections with interactive visualizations\n", "- **ML Models:** XGBoost, LightGBM, Random Forest, Neural Networks, Ensemble Methods\n", "- **Model Persistence:** Comprehensive model saving and loading utilities\n", "- **Business Intelligence:** Customer recommendations, market analysis, investment insights\n", "- **Production Ready:** Error handling, documentation, scalable architecture\n", "\n", "---\n", "\n", "## 📑 **Table of Contents**\n", "\n", "### **PART 1: VISUALIZATION & EDA** 📊\n", "1. [Environment Setup & Data Loading](#1-environment-setup--data-loading)\n", "2. [Dataset Overview & Basic Statistics](#2-dataset-overview--basic-statistics)\n", "3. [Target Variable Analysis](#3-target-variable-analysis)\n", "4. [Missing Data Analysis](#4-missing-data-analysis)\n", "5. [Numerical Features Analysis](#5-numerical-features-analysis)\n", "6. [Categorical Features Analysis](#6-categorical-features-analysis)\n", "7. [Correlation Analysis](#7-correlation-analysis)\n", "8. [Feature Relationships](#8-feature-relationships)\n", "9. [Outlier Analysis](#9-outlier-analysis)\n", "10. [Geographic Analysis](#10-geographic-analysis)\n", "11. [Advanced Visualizations](#11-advanced-visualizations)\n", "\n", "### **PART 2: DATA PREPROCESSING** 🔧\n", "12. [Data Cleaning & Missing Value Treatment](#12-data-cleaning--missing-value-treatment)\n", "13. [Feature Engineering](#13-feature-engineering)\n", "14. [Data Validation & Quality Checks](#14-data-validation--quality-checks)\n", "15. [Preprocessed Data Saving & Loading](#15-preprocessed-data-saving)\n", "\n", "### **PART 3: MODEL BUILDING** 🤖\n", "16. [Model Building Setup](#16-model-building-setup)\n", "17. [Baseline Models](#17-baseline-models)\n", "18. [Advanced Gradient Boosting Models](#18-advanced-gradient-boosting-models)\n", "19. [Ensemble Methods](#19-ensemble-methods)\n", "20. [Hyperparameter Optimization](#20-hyperparameter-optimization)\n", "21. [Model Evaluation & Comparison](#21-model-evaluation--comparison)\n", "22. [Model Persistence & Saving](#22-model-persistence--saving)\n", "\n", "### **PART 4: <PERSON><PERSON><PERSON> INTERPRETATION** 🔍\n", "23. [Feature Importance Analysis](#23-feature-importance-analysis)\n", "24. [SHAP Analysis](#24-shap-analysis)\n", "25. [Partial Dependence Analysis](#25-partial-dependence-analysis)\n", "26. [Business Insights & Recommendations](#26-business-insights--recommendations)\n", "\n", "### **PART 5: BUSINESS APPLICATION** 💼\n", "27. [Customer Profile System](#27-customer-profile-system)\n", "28. [House Recommendation Engine](#28-house-recommendation-engine)\n", "29. [Price Prediction Service](#29-price-prediction-service)\n", "30. [Investment Analysis Tools](#30-investment-analysis-tools)\n", "31. [Market Insights Dashboard](#31-market-insights-dashboard)\n", "\n", "### **PART 6: COMPLETE DEMO** 🎯\n", "32. [End-to-End System Demo](#32-end-to-end-system-demo)\n", "33. [Production Deployment Guide](#33-production-deployment-guide)\n", "34. [Final Summary & Next Steps](#34-final-summary--next-steps)\n", "\n", "---\n", "\n", "## 🎉 **Ready to Begin!**\n", "\n", "This notebook represents the complete house price prediction system, suitable for:\n", "- **Academic Projects:** Comprehensive analysis for internship requirements\n", "- **Business Applications:** Real-world deployment and decision making\n", "- **Learning:** Understanding end-to-end ML project development\n", "- **Portfolio:** Demonstrating advanced data science capabilities\n", "\n", "Let's start with the comprehensive analysis! 🚀"], "metadata": {"id": "main_title"}}, {"cell_type": "markdown", "source": ["---\n", "\n", "# 📊 PART 1: VISUALIZATION & EDA\n", "\n", "## 1. Environment Setup & Data Loading\n", "\n", "Setting up the complete environment with all necessary libraries for comprehensive analysis."], "metadata": {"id": "part1_title"}}, {"cell_type": "code", "execution_count": null, "metadata": {"id": "environment_setup"}, "outputs": [], "source": ["# ===== COMPREHENSIVE LIBRARY IMPORTS =====\n", "print(\"🚀 Setting up comprehensive analysis environment...\")\n", "\n", "# Core libraries\n", "import pandas as pd\n", "import numpy as np\n", "import warnings\n", "warnings.filterwarnings('ignore')\n", "\n", "# Visualization libraries\n", "import matplotlib.pyplot as plt\n", "import seaborn as sns\n", "import plotly.express as px\n", "import plotly.graph_objects as go\n", "from plotly.subplots import make_subplots\n", "import plotly.figure_factory as ff\n", "\n", "# Statistical libraries\n", "from scipy import stats\n", "from scipy.stats import normaltest, skew, kurtosis, pearsonr, spearmanr\n", "try:\n", "    import missingno as msno\n", "    print(\"✅ Missing data visualization library loaded\")\n", "except ImportError:\n", "    print(\"⚠️ missingno not available. Install with: pip install missingno\")\n", "    msno = None\n", "\n", "# Machine Learning libraries\n", "from sklearn.model_selection import train_test_split, cross_val_score, KFold\n", "from sklearn.preprocessing import StandardScaler, LabelEncoder, RobustScaler\n", "from sklearn.metrics import mean_squared_error, mean_absolute_error, r2_score\n", "from sklearn.ensemble import RandomForestRegressor, VotingRegressor, GradientBoostingRegressor\n", "from sklearn.linear_model import Ridge, Lasso, ElasticNet, LinearRegression\n", "from sklearn.neighbors import NearestNeighbors\n", "from sklearn.inspection import permutation_importance\n", "\n", "# Advanced ML libraries\n", "try:\n", "    import xgboost as xgb\n", "    print(\"✅ XGBoost imported successfully\")\n", "    xgb_available = True\n", "except ImportError:\n", "    print(\"⚠️ XGBoost not available. Install with: pip install xgboost\")\n", "    xgb_available = False\n", "\n", "try:\n", "    import lightgbm as lgb\n", "    print(\"✅ LightGBM imported successfully\")\n", "    lgb_available = True\n", "except ImportError:\n", "    print(\"⚠️ LightGBM not available. Install with: pip install lightgbm\")\n", "    lgb_available = False\n", "\n", "# Model interpretation libraries\n", "try:\n", "    import shap\n", "    print(\"✅ SHAP imported successfully\")\n", "    shap_available = True\n", "except ImportError:\n", "    print(\"⚠️ SHAP not available. Install with: pip install shap\")\n", "    shap_available = False\n", "\n", "# Utilities\n", "import joblib\n", "import pickle\n", "import json\n", "import os\n", "import time\n", "import itertools\n", "from datetime import datetime\n", "\n", "# Set display options\n", "pd.set_option('display.max_columns', None)\n", "pd.set_option('display.max_rows', 100)\n", "pd.set_option('display.float_format', '{:.3f}'.format)\n", "\n", "# Set plot styles\n", "plt.style.use('seaborn-v0_8')\n", "sns.set_palette(\"husl\")\n", "plt.rcParams['figure.figsize'] = (12, 8)\n", "plt.rcParams['font.size'] = 10\n", "\n", "# Configure plotly\n", "import plotly.io as pio\n", "pio.templates.default = \"plotly_white\"\n", "\n", "print(\"\\n🎉 Environment setup complete!\")\n", "print(f\"📅 Analysis Date: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\")\n", "print(\"📦 All libraries loaded successfully!\")\n", "print(\"=\" * 60)"]}, {"cell_type": "code", "source": ["# ===== DATA LOADING WITH MULTIPLE SOURCE SUPPORT =====\n", "print(\"📊 Loading House Price Dataset...\")\n", "\n", "# Define possible data paths\n", "data_paths = [\n", "    'data.csv',  # Current directory\n", "    '../data.csv',  # Parent directory\n", "    '../../data.csv',  # Two levels up\n", "    'processed_data/house_price_preprocessed_data.csv',  # Preprocessed data\n", "    '../processed_data/house_price_preprocessed_data.csv'\n", "]\n", "\n", "df = None\n", "data_loaded = False\n", "data_source = None\n", "\n", "# Try to load data from different paths\n", "for i, path in enumerate(data_paths):\n", "    if os.path.exists(path):\n", "        try:\n", "            df = pd.read_csv(path)\n", "            data_source = path\n", "            data_loaded = True\n", "            print(f\"✅ Dataset loaded successfully from: {path}\")\n", "            break\n", "        except Exception as e:\n", "            print(f\"⚠️ Error loading from {path}: {e}\")\n", "            continue\n", "\n", "if not data_loaded:\n", "    print(\"❌ No data files found!\")\n", "    print(\"Please ensure data.csv is available in one of these locations:\")\n", "    for path in data_paths:\n", "        print(f\"  • {path}\")\n", "    df = None\n", "else:\n", "    print(f\"\\n📈 DATASET OVERVIEW\")\n", "    print(f\"📁 Source: {data_source}\")\n", "    print(f\"📊 Shape: {df.shape}\")\n", "    print(f\"🏘️ Total Properties: {len(df):,}\")\n", "    print(f\"📋 Total Features: {df.shape[1]}\")\n", "    print(f\"💾 Memory Usage: {df.memory_usage(deep=True).sum() / 1024**2:.2f} MB\")\n", "    \n", "    # Basic data quality check\n", "    missing_count = df.isnull().sum().sum()\n", "    missing_percentage = (missing_count / (df.shape[0] * df.shape[1])) * 100\n", "    print(f\"🔍 Missing Values: {missing_count:,} ({missing_percentage:.1f}%)\")\n", "    \n", "    # Check for target variable\n", "    if 'SalePrice' in df.columns:\n", "        print(f\"💰 Price Range: ${df['SalePrice'].min():,} - ${df['SalePrice'].max():,}\")\n", "        print(f\"💵 Average Price: ${df['SalePrice'].mean():,.0f}\")\n", "        print(f\"📊 Median Price: ${df['SalePrice'].median():,.0f}\")\n", "        target_available = True\n", "    else:\n", "        print(\"⚠️ SalePrice column not found - will create sample target for demo\")\n", "        target_available = False\n", "    \n", "    print(\"=\" * 60)"], "metadata": {"id": "data_loading"}, "execution_count": null, "outputs": []}, {"cell_type": "markdown", "source": ["## 2. Dataset Overview & Basic Statistics\n", "\n", "Comprehensive overview of the dataset structure, data types, and basic statistical properties."], "metadata": {"id": "dataset_overview"}}, {"cell_type": "code", "source": ["# ===== COMPREHENSIVE DATASET OVERVIEW =====\n", "if data_loaded and df is not None:\n", "    print(\"📋 COMPREHENSIVE DATASET ANALYSIS\")\n", "    print(\"=\" * 50)\n", "    \n", "    # Basic dataset information\n", "    print(f\"\\n🏠 DATASET SUMMARY:\")\n", "    print(f\"  • Dataset Shape: {df.shape}\")\n", "    print(f\"  • Number of Properties: {df.shape[0]:,}\")\n", "    print(f\"  • Number of Features: {df.shape[1] - 1 if 'SalePrice' in df.columns else df.shape[1]}\")\n", "    print(f\"  • Memory Usage: {df.memory_usage(deep=True).sum() / 1024**2:.2f} MB\")\n", "    \n", "    # Data types analysis\n", "    print(f\"\\n📊 DATA TYPES BREAKDOWN:\")\n", "    dtype_counts = df.dtypes.value_counts()\n", "    for dtype, count in dtype_counts.items():\n", "        print(f\"  • {dtype}: {count} columns\")\n", "    \n", "    # Separate numerical and categorical columns\n", "    numerical_cols = df.select_dtypes(include=[np.number]).columns.tolist()\n", "    categorical_cols = df.select_dtypes(include=['object']).columns.tolist()\n", "    \n", "    # Remove target and ID columns from features\n", "    if 'SalePrice' in numerical_cols:\n", "        numerical_cols.remove('SalePrice')\n", "    if 'Id' in numerical_cols:\n", "        numerical_cols.remove('Id')\n", "    \n", "    print(f\"\\n🔢 FEATURE CATEGORIES:\")\n", "    print(f\"  • Numerical Features: {len(numerical_cols)}\")\n", "    print(f\"  • Categorical Features: {len(categorical_cols)}\")\n", "    print(f\"  • Total Features: {len(numerical_cols) + len(categorical_cols)}\")\n", "    \n", "    # Display first few rows\n", "    print(f\"\\n👀 FIRST 5 ROWS:\")\n", "    display(df.head())\n", "    \n", "    # Basic statistics for numerical columns\n", "    if len(numerical_cols) > 0:\n", "        print(f\"\\n📈 NUMERICAL FEATURES STATISTICS:\")\n", "        numerical_stats = df[numerical_cols].describe()\n", "        display(numerical_stats)\n", "    \n", "    # Information about categorical columns\n", "    if len(categorical_cols) > 0:\n", "        print(f\"\\n📝 CATEGORICAL FEATURES OVERVIEW:\")\n", "        cat_info = []\n", "        for col in categorical_cols[:10]:  # Show first 10 categorical columns\n", "            unique_count = df[col].nunique()\n", "            most_common = df[col].mode()[0] if not df[col].mode().empty else 'N/A'\n", "            missing_count = df[col].isnull().sum()\n", "            cat_info.append({\n", "                'Column': col,\n", "                'Unique_Values': unique_count,\n", "                'Most_Common': most_common,\n", "                'Missing_Values': missing_count\n", "            })\n", "        \n", "        cat_df = pd.DataFrame(cat_info)\n", "        display(cat_df)\n", "        \n", "        if len(categorical_cols) > 10:\n", "            print(f\"... and {len(categorical_cols) - 10} more categorical columns\")\n", "    \n", "    print(\"\\n✅ Dataset overview complete!\")\n", "    print(\"=\" * 60)\n", "\n", "else:\n", "    print(\"❌ Cannot display dataset overview - data not loaded\")"], "metadata": {"id": "dataset_overview_code"}, "execution_count": null, "outputs": []}, {"cell_type": "markdown", "source": ["## 3. Target Variable Analysis\n", "\n", "Comprehensive analysis of the target variable (SalePrice) including distribution, statistical properties, and outlier detection."], "metadata": {"id": "target_analysis"}}, {"cell_type": "code", "source": ["# ===== TARGET VARIABLE COMPREHENSIVE ANALYSIS =====\n", "if data_loaded and df is not None and 'SalePrice' in df.columns:\n", "    print(\"🎯 TARGET VARIABLE ANALYSIS: SalePrice\")\n", "    print(\"=\" * 50)\n", "    \n", "    target = df['SalePrice']\n", "    \n", "    # Basic statistics\n", "    print(f\"\\n📊 BASIC STATISTICS:\")\n", "    print(f\"  • Count: {target.count():,}\")\n", "    print(f\"  • Mean: ${target.mean():,.2f}\")\n", "    print(f\"  • Median: ${target.median():,.2f}\")\n", "    print(f\"  • Standard Deviation: ${target.std():,.2f}\")\n", "    print(f\"  • Minimum: ${target.min():,.2f}\")\n", "    print(f\"  • Maximum: ${target.max():,.2f}\")\n", "    print(f\"  • Range: ${target.max() - target.min():,.2f}\")\n", "    \n", "    # Quartiles and percentiles\n", "    print(f\"\\n📈 QUARTILES & PERCENTILES:\")\n", "    percentiles = [5, 10, 25, 50, 75, 90, 95, 99]\n", "    for p in percentiles:\n", "        value = np.percentile(target, p)\n", "        print(f\"  • {p}th percentile: ${value:,.0f}\")\n", "    \n", "    # Distribution properties\n", "    print(f\"\\n📉 DISTRIBUTION PROPERTIES:\")\n", "    skewness = skew(target)\n", "    kurt = kurtosis(target)\n", "    print(f\"  • Skewness: {skewness:.3f} ({'Right-skewed' if skewness > 0 else 'Left-skewed' if skewness < 0 else 'Symmetric'})\")\n", "    print(f\"  • Kurtosis: {kurt:.3f} ({'Heavy-tailed' if kurt > 0 else 'Light-tailed' if kurt < 0 else 'Normal-tailed'})\")\n", "    \n", "    # Normality test\n", "    stat, p_value = normaltest(target)\n", "    print(f\"  • Normality Test (D'Agostino): p-value = {p_value:.2e}\")\n", "    print(f\"  • Distribution: {'Not Normal' if p_value < 0.05 else 'Approximately Normal'} (α = 0.05)\")\n", "    \n", "    # Coefficient of variation\n", "    cv = (target.std() / target.mean()) * 100\n", "    print(f\"  • Coefficient of Variation: {cv:.1f}%\")\n", "    \n", "    # Visualizations\n", "    print(f\"\\n📊 CREATING COMPREHENSIVE VISUALIZATIONS...\")\n", "    \n", "    # Create subplot figure\n", "    fig, axes = plt.subplots(2, 3, figsize=(18, 12))\n", "    fig.suptitle('SalePrice - Comprehensive Target Variable Analysis', fontsize=16, fontweight='bold')\n", "    \n", "    # 1. Histo<PERSON> with K<PERSON>\n", "    axes[0, 0].hist(target, bins=50, alpha=0.7, color='skyblue', edgecolor='black', density=True)\n", "    axes[0, 0].axvline(target.mean(), color='red', linestyle='--', linewidth=2, label=f'Mean: ${target.mean():,.0f}')\n", "    axes[0, 0].axvline(target.median(), color='green', linestyle='--', linewidth=2, label=f'Median: ${target.median():,.0f}')\n", "    \n", "    # Add KDE\n", "    from scipy.stats import gaussian_kde\n", "    kde = gaussian_kde(target)\n", "    x_range = np.linspace(target.min(), target.max(), 100)\n", "    axes[0, 0].plot(x_range, kde(x_range), 'orange', linewidth=2, label='KDE')\n", "    \n", "    axes[0, 0].set_title('Distribution with KDE')\n", "    axes[0, 0].set_xlabel('Sale Price ($)')\n", "    axes[0, 0].set_ylabel('Density')\n", "    axes[0, 0].legend()\n", "    axes[0, 0].grid(True, alpha=0.3)\n", "    \n", "    # 2. Box plot\n", "    box_plot = axes[0, 1].boxplot(target, patch_artist=True, notch=True)\n", "    box_plot['boxes'][0].set_facecolor('lightcoral')\n", "    axes[0, 1].set_title('Box Plot (with <PERSON><PERSON>)')\n", "    axes[0, 1].set_ylabel('Sale Price ($)')\n", "    axes[0, 1].grid(True, alpha=0.3)\n", "    \n", "    # 3. Q-Q plot\n", "    stats.probplot(target, dist=\"norm\", plot=axes[0, 2])\n", "    axes[0, 2].set_title('Q-Q Plot (Normal Distribution)')\n", "    axes[0, 2].grid(True, alpha=0.3)\n", "    \n", "    # 4. Log-transformed distribution\n", "    log_target = np.log1p(target)  # log(1+x) to handle zeros\n", "    axes[1, 0].hist(log_target, bins=50, alpha=0.7, color='lightgreen', edgecolor='black')\n", "    axes[1, 0].axvline(log_target.mean(), color='red', linestyle='--', linewidth=2, label=f'Mean: {log_target.mean():.2f}')\n", "    axes[1, 0].axvline(log_target.median(), color='green', linestyle='--', linewidth=2, label=f'Median: {log_target.median():.2f}')\n", "    axes[1, 0].set_title('Log-Transformed Distribution')\n", "    axes[1, 0].set_xlabel('Log(Sale Price + 1)')\n", "    axes[1, 0].set_ylabel('Frequency')\n", "    axes[1, 0].legend()\n", "    axes[1, 0].grid(True, alpha=0.3)\n", "    \n", "    # 5. Cumulative distribution\n", "    sorted_prices = np.sort(target)\n", "    cumulative_prob = np.arange(1, len(sorted_prices) + 1) / len(sorted_prices)\n", "    axes[1, 1].plot(sorted_prices, cumulative_prob, linewidth=2, color='purple')\n", "    axes[1, 1].set_title('Cumulative Distribution Function')\n", "    axes[1, 1].set_xlabel('Sale Price ($)')\n", "    axes[1, 1].set_ylabel('Cumulative Probability')\n", "    axes[1, 1].grid(True, alpha=0.3)\n", "    \n", "    # 6. Price ranges analysis\n", "    price_ranges = ['<$100K', '$100K-$200K', '$200K-$300K', '$300K-$400K', '$400K-$500K', '>$500K']\n", "    range_counts = [\n", "        (target < 100000).sum(),\n", "        ((target >= 100000) & (target < 200000)).sum(),\n", "        ((target >= 200000) & (target < 300000)).sum(),\n", "        ((target >= 300000) & (target < 400000)).sum(),\n", "        ((target >= 400000) & (target < 500000)).sum(),\n", "        (target >= 500000).sum()\n", "    ]\n", "    \n", "    colors = plt.cm.Set3(np.linspace(0, 1, len(price_ranges)))\n", "    bars = axes[1, 2].bar(price_ranges, range_counts, color=colors, edgecolor='black')\n", "    axes[1, 2].set_title('Properties by Price Range')\n", "    axes[1, 2].set_xlabel('Price Range')\n", "    axes[1, 2].set_ylabel('Number of Properties')\n", "    axes[1, 2].tick_params(axis='x', rotation=45)\n", "    \n", "    # Add value labels on bars\n", "    for bar, count in zip(bars, range_counts):\n", "        height = bar.get_height()\n", "        axes[1, 2].text(bar.get_x() + bar.get_width()/2., height + 5,\n", "                       f'{count}', ha='center', va='bottom', fontweight='bold')\n", "    \n", "    plt.tight_layout()\n", "    plt.show()\n", "    \n", "    # Log transformation analysis\n", "    print(f\"\\n🔄 LOG TRANSFORMATION ANALYSIS:\")\n", "    log_skewness = skew(log_target)\n", "    log_kurtosis = kurtosis(log_target)\n", "    print(f\"  • Original Skewness: {skewness:.3f}\")\n", "    print(f\"  • Log-transformed Skewness: {log_skewness:.3f}\")\n", "    print(f\"  • Improvement: {abs(skewness) - abs(log_skewness):+.3f}\")\n", "    print(f\"  • Recommendation: {'Use log transformation' if abs(log_skewness) < abs(skewness) else 'Keep original scale'}\")\n", "    \n", "    print(\"\\n✅ Target variable analysis complete!\")\n", "    print(\"=\" * 60)\n", "\n", "else:\n", "    print(\"❌ Cannot analyze target variable - SalePrice column not found or data not loaded\")"], "metadata": {"id": "target_analysis_code"}, "execution_count": null, "outputs": []}, {"cell_type": "markdown", "source": ["## 4. Missing Data Analysis\n", "\n", "Comprehensive analysis of missing data patterns, visualization, and impact assessment."], "metadata": {"id": "missing_data_analysis"}}, {"cell_type": "code", "source": ["# ===== COMPREHENSIVE MISSING DATA ANALYSIS =====\n", "if data_loaded and df is not None:\n", "    print(\"🔍 MISSING DATA COMPREHENSIVE ANALYSIS\")\n", "    print(\"=\" * 50)\n", "    \n", "    # Calculate missing data statistics\n", "    missing_counts = df.isnull().sum()\n", "    missing_percentages = (missing_counts / len(df)) * 100\n", "    total_missing = missing_counts.sum()\n", "    total_cells = df.shape[0] * df.shape[1]\n", "    overall_missing_percentage = (total_missing / total_cells) * 100\n", "    \n", "    print(f\"\\n📊 MISSING DATA SUMMARY:\")\n", "    print(f\"  • Total Missing Values: {total_missing:,}\")\n", "    print(f\"  • Total Cells: {total_cells:,}\")\n", "    print(f\"  • Overall Missing Percentage: {overall_missing_percentage:.2f}%\")\n", "    print(f\"  • Columns with Missing Data: {(missing_counts > 0).sum()}\")\n", "    print(f\"  • Complete Columns: {(missing_counts == 0).sum()}\")\n", "    \n", "    # Create missing data summary DataFrame\n", "    missing_df = pd.DataFrame({\n", "        'Column': missing_counts.index,\n", "        'Missing_Count': missing_counts.values,\n", "        'Missing_Percentage': missing_percentages.values,\n", "        'Data_Type': df.dtypes.values\n", "    })\n", "    \n", "    # Filter columns with missing data\n", "    missing_cols_df = missing_df[missing_df['Missing_Count'] > 0].sort_values('Missing_Percentage', ascending=False)\n", "    \n", "    if not missing_cols_df.empty:\n", "        print(f\"\\n📋 COLUMNS WITH MISSING DATA:\")\n", "        display(missing_cols_df)\n", "        \n", "        # Categorize missing data severity\n", "        high_missing = missing_cols_df[missing_cols_df['Missing_Percentage'] > 50]\n", "        medium_missing = missing_cols_df[(missing_cols_df['Missing_Percentage'] > 20) & (missing_cols_df['Missing_Percentage'] <= 50)]\n", "        low_missing = missing_cols_df[missing_cols_df['Missing_Percentage'] <= 20]\n", "        \n", "        print(f\"\\n🚨 MISSING DATA SEVERITY:\")\n", "        print(f\"  • High (>50%): {len(high_missing)} columns\")\n", "        print(f\"  • Medium (20-50%): {len(medium_missing)} columns\")\n", "        print(f\"  • Low (≤20%): {len(low_missing)} columns\")\n", "        \n", "        if len(high_missing) > 0:\n", "            print(f\"\\n⚠️ HIGH MISSING DATA COLUMNS:\")\n", "            for _, row in high_missing.iterrows():\n", "                print(f\"  • {row['Column']}: {row['Missing_Percentage']:.1f}%\")\n", "    \n", "    else:\n", "        print(f\"\\n✅ NO MISSING DATA FOUND!\")\n", "        print(f\"  • All {df.shape[1]} columns are complete\")\n", "        print(f\"  • Dataset is ready for analysis\")\n", "    \n", "    # Visualizations\n", "    if not missing_cols_df.empty:\n", "        print(f\"\\n📊 CREATING MISSING DATA VISUALIZATIONS...\")\n", "        \n", "        # Create visualization figure\n", "        fig, axes = plt.subplots(2, 2, figsize=(16, 12))\n", "        fig.suptitle('Missing Data Analysis - Comprehensive Overview', fontsize=16, fontweight='bold')\n", "        \n", "        # 1. Missing data heatmap (top 20 columns with missing data)\n", "        top_missing_cols = missing_cols_df.head(20)['Column'].tolist()\n", "        if len(top_missing_cols) > 0:\n", "            missing_matrix = df[top_missing_cols].isnull().astype(int)\n", "            sns.heatmap(missing_matrix.T, cbar=True, cmap='viridis', \n", "                       xticklabels=False, yticklabels=True, ax=axes[0, 0])\n", "            axes[0, 0].set_title(f'Missing Data Heatmap (Top {len(top_missing_cols)} Columns)')\n", "            axes[0, 0].set_xlabel('Records')\n", "            axes[0, 0].set_ylabel('Features')\n", "        \n", "        # 2. Missing data bar chart\n", "        top_15_missing = missing_cols_df.head(15)\n", "        bars = axes[0, 1].barh(range(len(top_15_missing)), top_15_missing['Missing_Percentage'], \n", "                              color='coral', edgecolor='black')\n", "        axes[0, 1].set_yticks(range(len(top_15_missing)))\n", "        axes[0, 1].set_yticklabels(top_15_missing['Column'])\n", "        axes[0, 1].set_xlabel('Missing Percentage (%)')\n", "        axes[0, 1].set_title('Top 15 Columns by Missing Data %')\n", "        axes[0, 1].grid(True, alpha=0.3)\n", "        \n", "        # Add percentage labels\n", "        for i, (bar, pct) in enumerate(zip(bars, top_15_missing['Missing_Percentage'])):\n", "            axes[0, 1].text(bar.get_width() + 0.5, bar.get_y() + bar.get_height()/2, \n", "                           f'{pct:.1f}%', ha='left', va='center', fontweight='bold')\n", "        \n", "        # 3. Missing data by data type\n", "        missing_by_type = missing_cols_df.groupby('Data_Type')['Missing_Count'].sum().sort_values(ascending=False)\n", "        if len(missing_by_type) > 0:\n", "            colors = plt.cm.Set2(np.linspace(0, 1, len(missing_by_type)))\n", "            wedges, texts, autotexts = axes[1, 0].pie(missing_by_type.values, labels=missing_by_type.index, \n", "                                                     autopct='%1.1f%%', colors=colors, startangle=90)\n", "            axes[1, 0].set_title('Missing Data Distribution by Data Type')\n", "        \n", "        # 4. Missing data severity distribution\n", "        severity_counts = [len(low_missing), len(medium_missing), len(high_missing)]\n", "        severity_labels = ['Low (≤20%)', 'Medium (20-50%)', 'High (>50%)']\n", "        severity_colors = ['lightgreen', 'orange', 'red']\n", "        \n", "        bars = axes[1, 1].bar(severity_labels, severity_counts, color=severity_colors, \n", "                             edgecolor='black', alpha=0.7)\n", "        axes[1, 1].set_title('Missing Data Severity Distribution')\n", "        axes[1, 1].set_ylabel('Number of Columns')\n", "        axes[1, 1].grid(True, alpha=0.3)\n", "        \n", "        # Add count labels on bars\n", "        for bar, count in zip(bars, severity_counts):\n", "            height = bar.get_height()\n", "            axes[1, 1].text(bar.get_x() + bar.get_width()/2., height + 0.1,\n", "                           f'{count}', ha='center', va='bottom', fontweight='bold')\n", "        \n", "        plt.tight_layout()\n", "        plt.show()\n", "        \n", "        # Missing data patterns analysis\n", "        print(f\"\\n🔍 MISSING DATA PATTERNS:\")\n", "        \n", "        # Check for completely missing rows\n", "        completely_missing_rows = df.isnull().all(axis=1).sum()\n", "        print(f\"  • Completely missing rows: {completely_missing_rows}\")\n", "        \n", "        # Check for rows with high missing percentage\n", "        row_missing_pct = (df.isnull().sum(axis=1) / df.shape[1]) * 100\n", "        high_missing_rows = (row_missing_pct > 50).sum()\n", "        print(f\"  • Rows with >50% missing data: {high_missing_rows}\")\n", "        \n", "        # Most common missing data combinations\n", "        if len(top_missing_cols) >= 2:\n", "            print(f\"\\n🔗 MISSING DATA CORRELATIONS (Top 5 pairs):\")\n", "            missing_corr_pairs = []\n", "            for i in range(min(5, len(top_missing_cols))):\n", "                for j in range(i+1, min(5, len(top_missing_cols))):\n", "                    col1, col2 = top_missing_cols[i], top_missing_cols[j]\n", "                    both_missing = (df[col1].isnull() & df[col2].isnull()).sum()\n", "                    if both_missing > 0:\n", "                        missing_corr_pairs.append((col1, col2, both_missing))\n", "            \n", "            missing_corr_pairs.sort(key=lambda x: x[2], reverse=True)\n", "            for col1, col2, count in missing_corr_pairs[:5]:\n", "                print(f\"  • {col1} & {col2}: {count} records\")\n", "    \n", "    # Missing data recommendations\n", "    print(f\"\\n💡 MISSING DATA HANDLING RECOMMENDATIONS:\")\n", "    if missing_cols_df.empty:\n", "        print(f\"  • ✅ No action needed - dataset is complete\")\n", "    else:\n", "        print(f\"  • 🔧 Columns to consider for removal (>70% missing): {len(missing_cols_df[missing_cols_df['Missing_Percentage'] > 70])}\")\n", "        print(f\"  • 📊 Numerical columns for median imputation: {len(missing_cols_df[missing_cols_df['Data_Type'].isin(['int64', 'float64'])])}\")\n", "        print(f\"  • 📝 Categorical columns for mode imputation: {len(missing_cols_df[missing_cols_df['Data_Type'] == 'object'])}\")\n", "        print(f\"  • 🎯 Consider advanced imputation for: {len(missing_cols_df[(missing_cols_df['Missing_Percentage'] > 5) & (missing_cols_df['Missing_Percentage'] <= 30)])} columns\")\n", "    \n", "    print(\"\\n✅ Missing data analysis complete!\")\n", "    print(\"=\" * 60)\n", "\n", "else:\n", "    print(\"❌ Cannot analyze missing data - data not loaded\")"], "metadata": {"id": "missing_data_analysis_code"}, "execution_count": null, "outputs": []}, {"cell_type": "markdown", "source": ["## 11. Advanced Visualizations\n", "\n", "Comprehensive visualization suite including line plots, violin plots, and other advanced chart types for deeper data insights."], "metadata": {"id": "advanced_visualizations"}}, {"cell_type": "code", "source": ["# ===== ADVANCED VISUALIZATIONS - PART 1: VIOLIN PLOTS =====\n", "if df_processed is not None:\n", "    print(\"📊 ADVANCED VISUALIZATIONS - COMPREHENSIVE ANALYSIS\")\n", "    print(\"=\" * 60)\n", "    \n", "    # Prepare data for visualizations\n", "    numerical_cols = df_processed.select_dtypes(include=[np.number]).columns.tolist()\n", "    categorical_cols = df_processed.select_dtypes(include=['object']).columns.tolist()\n", "    \n", "    # Remove ID and target from numerical features for some plots\n", "    viz_numerical_cols = [col for col in numerical_cols if col not in ['Id', 'SalePrice']]\n", "    \n", "    print(f\"\\n🎨 VISUALIZATION SETUP:\")\n", "    print(f\"  • Numerical columns for visualization: {len(viz_numerical_cols)}\")\n", "    print(f\"  • Categorical columns for visualization: {len(categorical_cols)}\")\n", "    print(f\"  • Target variable: {'SalePrice' if 'SalePrice' in df_processed.columns else 'Not available'}\")\n", "    \n", "    # 1. VIOLIN PLOTS - Price Distribution by Key Categories\n", "    if 'SalePrice' in df_processed.columns and len(categorical_cols) > 0:\n", "        print(f\"\\n🎻 CREATING VIOLIN PLOTS...\")\n", "        \n", "        # Select key categorical columns for violin plots\n", "        key_cat_cols = [col for col in ['MSZoning', 'Neighborhood', 'BldgType', 'HouseStyle', 'SaleCondition'] \n", "                       if col in categorical_cols][:3]  # Take first 3 available\n", "        \n", "        if key_cat_cols:\n", "            fig, axes = plt.subplots(1, len(key_cat_cols), figsize=(6*len(key_cat_cols), 8))\n", "            if len(key_cat_cols) == 1:\n", "                axes = [axes]\n", "            \n", "            fig.suptitle('Price Distribution by Categories - Violin Plots', fontsize=16, fontweight='bold')\n", "            \n", "            for i, col in enumerate(key_cat_cols):\n", "                # Limit categories to top 8 for readability\n", "                top_categories = df_processed[col].value_counts().head(8).index.tolist()\n", "                filtered_data = df_processed[df_processed[col].isin(top_categories)]\n", "                \n", "                sns.violinplot(data=filtered_data, x=col, y='SalePrice', ax=axes[i])\n", "                axes[i].set_title(f'Price Distribution by {col}')\n", "                axes[i].tick_params(axis='x', rotation=45)\n", "                axes[i].yaxis.set_major_formatter(plt.FuncFormatter(lambda x, p: f'${x/1000:.0f}K'))\n", "                axes[i].grid(True, alpha=0.3)\n", "            \n", "            plt.tight_layout()\n", "            plt.show()\n", "    \n", "    print(\"\\n✅ Violin plots complete!\")\n", "    print(\"=\" * 60)\n", "\n", "else:\n", "    print(\"❌ Cannot create advanced visualizations - data not available\")"], "metadata": {"id": "advanced_viz_1"}, "execution_count": null, "outputs": []}, {"cell_type": "code", "source": ["# ===== ADVANCED VISUALIZATIONS - PART 2: LINE PLOTS =====\n", "if df_processed is not None and 'SalePrice' in df_processed.columns:\n", "    print(\"📈 ADVANCED VISUALIZATIONS - LINE PLOTS\")\n", "    print(\"=\" * 50)\n", "    \n", "    # 2. LINE PLOTS - Trends over time and continuous variables\n", "    print(f\"\\n📊 CREATING LINE PLOTS...\")\n", "    \n", "    # Select key numerical columns for line plots\n", "    line_plot_cols = [col for col in ['YearBuilt', 'YearRemodAdd', 'GrLivArea', 'LotArea'] \n", "                     if col in df_processed.columns][:4]\n", "    \n", "    if line_plot_cols:\n", "        fig, axes = plt.subplots(2, 2, figsize=(16, 12))\n", "        axes = axes.flatten()\n", "        fig.suptitle('Trend Analysis - Line Plots', fontsize=16, fontweight='bold')\n", "        \n", "        for i, col in enumerate(line_plot_cols):\n", "            if i < 4:  # Ensure we don't exceed subplot count\n", "                # Create binned data for line plots\n", "                if col in ['YearBuilt', 'YearRemodAdd']:\n", "                    # For year columns, group by decade\n", "                    df_processed['decade'] = (df_processed[col] // 10) * 10\n", "                    trend_data = df_processed.groupby('decade')['SalePrice'].agg(['mean', 'count']).reset_index()\n", "                    trend_data = trend_data[trend_data['count'] >= 5]  # Filter decades with at least 5 houses\n", "                    \n", "                    axes[i].plot(trend_data['decade'], trend_data['mean'], marker='o', linewidth=2, markersize=6)\n", "                    axes[i].set_xlabel('Decade')\n", "                    axes[i].set_ylabel('Average Sale Price ($)')\n", "                    axes[i].set_title(f'Average Price Trend by {col} (Decade)')\n", "                    \n", "                else:\n", "                    # For area columns, create quantile-based bins\n", "                    df_processed['area_bin'] = pd.qcut(df_processed[col], q=10, duplicates='drop')\n", "                    trend_data = df_processed.groupby('area_bin')['SalePrice'].mean().reset_index()\n", "                    \n", "                    # Extract midpoint of intervals for x-axis\n", "                    trend_data['midpoint'] = trend_data['area_bin'].apply(lambda x: x.mid)\n", "                    \n", "                    axes[i].plot(trend_data['midpoint'], trend_data['SalePrice'], marker='o', linewidth=2, markersize=6)\n", "                    axes[i].set_xlabel(f'{col} (Binned)')\n", "                    axes[i].set_ylabel('Average Sale Price ($)')\n", "                    axes[i].set_title(f'Price Trend by {col}')\n", "                \n", "                axes[i].grid(True, alpha=0.3)\n", "                axes[i].yaxis.set_major_formatter(plt.FuncFormatter(lambda x, p: f'${x/1000:.0f}K'))\n", "        \n", "        # Hide empty subplots\n", "        for j in range(len(line_plot_cols), 4):\n", "            axes[j].set_visible(False)\n", "        \n", "        plt.tight_layout()\n", "        plt.show()\n", "    \n", "    print(\"\\n✅ Line plots complete!\")\n", "    print(\"=\" * 60)\n", "\n", "else:\n", "    print(\"❌ Cannot create line plots - data or target variable not available\")"], "metadata": {"id": "advanced_viz_2"}, "execution_count": null, "outputs": []}, {"cell_type": "code", "source": ["# ===== ADVANCED VISUALIZATIONS - PART 3: ENHANCED HEATMAPS =====\n", "if df_processed is not None:\n", "    print(\"🔥 ADVANCED VISUALIZATIONS - ENHANCED HEATMAPS\")\n", "    print(\"=\" * 50)\n", "    \n", "    # 3. CORRELATION HEATMAP - Enhanced version\n", "    print(f\"\\n🌡️ CREATING ENHANCED CORRELATION HEATMAP...\")\n", "    \n", "    # Select top numerical columns for correlation\n", "    corr_cols = [col for col in ['SalePrice', 'OverallQual', 'GrLivArea', 'GarageCars', \n", "                                'TotalBsmtSF', 'FullBath', 'YearBuilt', 'LotArea'] \n", "                if col in df_processed.columns][:8]\n", "    \n", "    if len(corr_cols) >= 3:\n", "        # Calculate correlation matrix\n", "        corr_matrix = df_processed[corr_cols].corr()\n", "        \n", "        # Create enhanced heatmap\n", "        plt.figure(figsize=(12, 10))\n", "        mask = np.triu(np.ones_like(corr_matrix, dtype=bool))  # Mask upper triangle\n", "        \n", "        sns.heatmap(corr_matrix, mask=mask, annot=True, cmap='RdYlBu_r', center=0,\n", "                   square=True, linewidths=0.5, cbar_kws={\"shrink\": .8}, fmt='.3f')\n", "        \n", "        plt.title('Enhanced Correlation Heatmap - Key Features', fontsize=16, fontweight='bold')\n", "        plt.tight_layout()\n", "        plt.show()\n", "    \n", "    print(\"\\n✅ Enhanced heatmap complete!\")\n", "    print(\"=\" * 60)\n", "\n", "else:\n", "    print(\"❌ Cannot create heatmaps - data not available\")"], "metadata": {"id": "advanced_viz_3"}, "execution_count": null, "outputs": []}, {"cell_type": "markdown", "source": ["---\n", "\n", "# 🔧 PART 2: DATA PREPROCESSING\n", "\n", "## 12. Data Cleaning & Missing Value Treatment\n", "\n", "Comprehensive data cleaning including missing value imputation, outlier treatment, and data validation."], "metadata": {"id": "part2_title"}}, {"cell_type": "code", "source": ["# ===== COMPREHENSIVE DATA PREPROCESSING =====\n", "if data_loaded and df is not None:\n", "    print(\"🔧 COMPREHENSIVE DATA PREPROCESSING\")\n", "    print(\"=\" * 50)\n", "    \n", "    # Create a copy for preprocessing\n", "    df_processed = df.copy()\n", "    original_shape = df_processed.shape\n", "    \n", "    print(f\"\\n📊 STARTING PREPROCESSING:\")\n", "    print(f\"  • Original Shape: {original_shape}\")\n", "    print(f\"  • Original Memory: {df_processed.memory_usage(deep=True).sum() / 1024**2:.2f} MB\")\n", "    \n", "    # Separate numerical and categorical columns\n", "    numerical_cols = df_processed.select_dtypes(include=[np.number]).columns.tolist()\n", "    categorical_cols = df_processed.select_dtypes(include=['object']).columns.tolist()\n", "    \n", "    # Remove target and ID columns from features\n", "    feature_numerical_cols = [col for col in numerical_cols if col not in ['SalePrice', 'Id']]\n", "    feature_categorical_cols = categorical_cols.copy()\n", "    \n", "    print(f\"\\n🔢 FEATURE CATEGORIES:\")\n", "    print(f\"  • Numerical Features: {len(feature_numerical_cols)}\")\n", "    print(f\"  • Categorical Features: {len(feature_categorical_cols)}\")\n", "    \n", "    # 1. MISSING VALUE TREATMENT\n", "    print(f\"\\n🔍 MISSING VALUE TREATMENT:\")\n", "    \n", "    missing_before = df_processed.isnull().sum().sum()\n", "    print(f\"  • Missing values before treatment: {missing_before:,}\")\n", "    \n", "    # Handle numerical missing values\n", "    numerical_imputation_log = []\n", "    for col in feature_numerical_cols:\n", "        missing_count = df_processed[col].isnull().sum()\n", "        if missing_count > 0:\n", "            # Use median for numerical columns\n", "            median_val = df_processed[col].median()\n", "            df_processed[col].fillna(median_val, inplace=True)\n", "            numerical_imputation_log.append({\n", "                'Column': col,\n", "                'Missing_Count': missing_count,\n", "                'Imputation_Value': median_val,\n", "                'Method': 'Median'\n", "            })\n", "    \n", "    # Handle categorical missing values\n", "    categorical_imputation_log = []\n", "    for col in feature_categorical_cols:\n", "        missing_count = df_processed[col].isnull().sum()\n", "        if missing_count > 0:\n", "            # Use mode for categorical columns, or 'Unknown' if no mode\n", "            mode_val = df_processed[col].mode()\n", "            imputation_val = mode_val[0] if not mode_val.empty else 'Unknown'\n", "            df_processed[col].fillna(imputation_val, inplace=True)\n", "            categorical_imputation_log.append({\n", "                'Column': col,\n", "                'Missing_Count': missing_count,\n", "                'Imputation_Value': imputation_val,\n", "                'Method': 'Mode' if not mode_val.empty else 'Unknown'\n", "            })\n", "    \n", "    missing_after = df_processed.isnull().sum().sum()\n", "    print(f\"  • Missing values after treatment: {missing_after:,}\")\n", "    print(f\"  • Missing values resolved: {missing_before - missing_after:,}\")\n", "    \n", "    # Display imputation summary\n", "    if numerical_imputation_log:\n", "        print(f\"\\n📊 NUMERICAL IMPUTATION SUMMARY:\")\n", "        num_impute_df = pd.DataFrame(numerical_imputation_log)\n", "        display(num_impute_df.head(10))\n", "        if len(numerical_imputation_log) > 10:\n", "            print(f\"... and {len(numerical_imputation_log) - 10} more numerical columns\")\n", "    \n", "    if categorical_imputation_log:\n", "        print(f\"\\n📝 CATEGORICAL IMPUTATION SUMMARY:\")\n", "        cat_impute_df = pd.DataFrame(categorical_imputation_log)\n", "        display(cat_impute_df.head(10))\n", "        if len(categorical_imputation_log) > 10:\n", "            print(f\"... and {len(categorical_imputation_log) - 10} more categorical columns\")\n", "    \n", "    print(\"\\n✅ Missing value treatment complete!\")\n", "    print(\"=\" * 60)\n", "\n", "else:\n", "    print(\"❌ Cannot perform preprocessing - data not loaded\")\n", "    df_processed = None"], "metadata": {"id": "data_preprocessing_code"}, "execution_count": null, "outputs": []}, {"cell_type": "code", "source": ["# ===== OUTLIER DETECTION AND TREATMENT =====\n", "if df_processed is not None:\n", "    print(\"🔍 OUTLIER DETECTION AND TREATMENT\")\n", "    print(\"=\" * 50)\n", "    \n", "    # Function to detect outliers using IQR method\n", "    def detect_outliers_iqr(data, column):\n", "        Q1 = data[column].quantile(0.25)\n", "        Q3 = data[column].quantile(0.75)\n", "        IQR = Q3 - Q1\n", "        lower_bound = Q1 - 1.5 * IQR\n", "        upper_bound = Q3 + 1.5 * IQR\n", "        outliers = data[(data[column] < lower_bound) | (data[column] > upper_bound)]\n", "        return outliers, lower_bound, upper_bound\n", "    \n", "    # Function to detect outliers using Z-score method\n", "    def detect_outliers_zscore(data, column, threshold=3):\n", "        z_scores = np.abs(stats.zscore(data[column]))\n", "        outliers = data[z_scores > threshold]\n", "        return outliers, threshold\n", "    \n", "    # Analyze outliers in key numerical columns\n", "    outlier_analysis = []\n", "    key_numerical_cols = feature_numerical_cols[:10]  # Analyze first 10 numerical columns\n", "    \n", "    print(f\"\\n📊 OUTLIER ANALYSIS (Top {len(key_numerical_cols)} numerical columns):\")\n", "    \n", "    for col in key_numerical_cols:\n", "        if col in df_processed.columns:\n", "            # IQR method\n", "            outliers_iqr, lower_iqr, upper_iqr = detect_outliers_iqr(df_processed, col)\n", "            \n", "            # Z-score method\n", "            outliers_zscore, threshold = detect_outliers_zscore(df_processed, col)\n", "            \n", "            outlier_analysis.append({\n", "                'Column': col,\n", "                'IQR_Outliers': len(outliers_iqr),\n", "                'IQR_Percentage': f\"{(len(outliers_iqr) / len(df_processed)) * 100:.2f}%\",\n", "                'ZScore_Outliers': len(outliers_zscore),\n", "                'ZScore_Percentage': f\"{(len(outliers_zscore) / len(df_processed)) * 100:.2f}%\",\n", "                'Lower_Bound': f\"{lower_iqr:.2f}\",\n", "                'Upper_Bound': f\"{upper_iqr:.2f}\"\n", "            })\n", "    \n", "    if outlier_analysis:\n", "        outlier_df = pd.DataFrame(outlier_analysis)\n", "        print(\"\\n📋 OUTLIER DETECTION SUMMARY:\")\n", "        display(outlier_df)\n", "        \n", "        # Outlier treatment recommendations\n", "        print(f\"\\n💡 OUTLIER TREATMENT RECOMMENDATIONS:\")\n", "        high_outlier_cols = [row['Column'] for _, row in outlier_df.iterrows() \n", "                           if float(row['IQR_Percentage'].rstrip('%')) > 5]\n", "        \n", "        if high_outlier_cols:\n", "            print(f\"  • Columns with >5% outliers (consider treatment): {len(high_outlier_cols)}\")\n", "            for col in high_outlier_cols[:5]:  # Show first 5\n", "                print(f\"    - {col}\")\n", "        else:\n", "            print(f\"  • ✅ No columns with excessive outliers (>5%)\")\n", "        \n", "        print(f\"  • 🎯 Consider log transformation for skewed features\")\n", "        print(f\"  • 🔧 Consider capping outliers at 95th/5th percentiles\")\n", "        print(f\"  • 📊 Consider robust scaling for features with outliers\")\n", "    \n", "    # Visualize outliers for top 3 columns with most outliers\n", "    if len(outlier_analysis) >= 3:\n", "        print(f\"\\n📊 CREATING OUTLIER VISUALIZATIONS...\")\n", "        \n", "        # Sort by IQR outlier percentage and get top 3\n", "        outlier_df_sorted = outlier_df.copy()\n", "        outlier_df_sorted['IQR_Pct_Numeric'] = outlier_df_sorted['IQR_Percentage'].str.rstrip('%').astype(float)\n", "        top_outlier_cols = outlier_df_sorted.nlargest(3, 'IQR_Pct_Numeric')['Column'].tolist()\n", "        \n", "        fig, axes = plt.subplots(1, 3, figsize=(18, 6))\n", "        fig.suptitle('Outlier Analysis - Top 3 Columns with Most Outliers', fontsize=16, fontweight='bold')\n", "        \n", "        for i, col in enumerate(top_outlier_cols):\n", "            # Box plot\n", "            box_plot = axes[i].boxplot(df_processed[col], patch_artist=True, notch=True)\n", "            box_plot['boxes'][0].set_facecolor('lightblue')\n", "            axes[i].set_title(f'{col}\\nOutliers: {outlier_df[outlier_df[\"Column\"] == col][\"IQR_Percentage\"].iloc[0]}')\n", "            axes[i].set_ylabel('Values')\n", "            axes[i].grid(True, alpha=0.3)\n", "        \n", "        plt.tight_layout()\n", "        plt.show()\n", "    \n", "    print(\"\\n✅ Outlier analysis complete!\")\n", "    print(\"📝 Note: Outliers detected but not automatically removed.\")\n", "    print(\"   Consider domain knowledge for outlier treatment decisions.\")\n", "    print(\"=\" * 60)\n", "\n", "else:\n", "    print(\"❌ Cannot perform outlier analysis - preprocessed data not available\")"], "metadata": {"id": "outlier_analysis_code"}, "execution_count": null, "outputs": []}, {"cell_type": "markdown", "source": ["## 15. Preprocessed Data Saving\n", "\n", "Save the preprocessed dataset for future use and model training, ensuring data persistence and reproducibility."], "metadata": {"id": "data_saving"}}, {"cell_type": "code", "source": ["# ===== PREPROCESSED DATA SAVING =====\n", "if df_processed is not None:\n", "    print(\"💾 PREPROCESSED DATA SAVING\")\n", "    print(\"=\" * 50)\n", "    \n", "    # Define the filename for the preprocessed data\n", "    preprocessed_file = 'house_price_preprocessed_data.csv'\n", "    \n", "    print(f\"\\n📁 SAVING PREPROCESSED DATA:\")\n", "    print(f\"  • File name: {preprocessed_file}\")\n", "    print(f\"  • Data shape: {df_processed.shape}\")\n", "    print(f\"  • Memory usage: {df_processed.memory_usage(deep=True).sum() / 1024**2:.2f} MB\")\n", "    \n", "    # Save the preprocessed DataFrame to a CSV file\n", "    try:\n", "        df_processed.to_csv(preprocessed_file, index=False)\n", "        file_size = os.path.getsize(preprocessed_file) / 1024**2\n", "        print(f\"\\n✅ Preprocessed data saved successfully to {preprocessed_file}\")\n", "        print(f\"  • File size: {file_size:.2f} MB\")\n", "        print(f\"  • Columns saved: {df_processed.shape[1]}\")\n", "        print(f\"  • Rows saved: {df_processed.shape[0]:,}\")\n", "        \n", "        # Save data summary\n", "        data_summary = {\n", "            'original_shape': original_shape,\n", "            'processed_shape': list(df_processed.shape),\n", "            'missing_values_resolved': missing_before - missing_after if 'missing_before' in locals() else 0,\n", "            'preprocessing_timestamp': datetime.now().isoformat(),\n", "            'file_path': preprocessed_file,\n", "            'file_size_mb': file_size\n", "        }\n", "        \n", "        summary_file = 'preprocessing_summary.json'\n", "        with open(summary_file, 'w') as f:\n", "            json.dump(data_summary, f, indent=2)\n", "        print(f\"  • Summary saved: {summary_file}\")\n", "        \n", "    except Exception as e:\n", "        print(f\"❌ Error saving preprocessed data: {e}\")\n", "        preprocessed_file = None\n", "    \n", "    print(\"\\n✅ Data saving complete!\")\n", "    print(\"=\" * 60)\n", "\n", "else:\n", "    print(\"❌ Cannot save data - no preprocessed data available\")\n", "    preprocessed_file = None"], "metadata": {"id": "data_saving_code"}, "execution_count": null, "outputs": []}, {"cell_type": "code", "source": ["# ===== PREPROCESSED DATA LOADING =====\n", "if preprocessed_file and os.path.exists(preprocessed_file):\n", "    print(\"📂 PREPROCESSED DATA LOADING\")\n", "    print(\"=\" * 50)\n", "    \n", "    # Read the preprocessed data back from the CSV file\n", "    try:\n", "        df_loaded = pd.read_csv(preprocessed_file)\n", "        print(f\"\\n✅ Preprocessed data reloaded successfully from {preprocessed_file}\")\n", "        print(f\"  • Shape: {df_loaded.shape}\")\n", "        print(f\"  • Memory usage: {df_loaded.memory_usage(deep=True).sum() / 1024**2:.2f} MB\")\n", "        print(f\"  • Data types: {df_loaded.dtypes.value_counts().to_dict()}\")\n", "        \n", "        # Display the first few rows of the reloaded data\n", "        print(f\"\\n📋 FIRST 5 ROWS OF RELOADED PREPROCESSED DATA:\")\n", "        try:\n", "            display(df_loaded.head())\n", "        except:\n", "            print(df_loaded.head())\n", "        \n", "        # Data validation checks\n", "        print(f\"\\n🔍 DATA VALIDATION:\")\n", "        missing_check = df_loaded.isnull().sum().sum()\n", "        print(f\"  • Missing values: {missing_check}\")\n", "        print(f\"  • Duplicate rows: {df_loaded.duplicated().sum()}\")\n", "        \n", "        if 'SalePrice' in df_loaded.columns:\n", "            print(f\"  • Target variable range: ${df_loaded['SalePrice'].min():,.0f} - ${df_loaded['SalePrice'].max():,.0f}\")\n", "            print(f\"  • Target variable mean: ${df_loaded['SalePrice'].mean():,.0f}\")\n", "        \n", "        # Optional: Assign back to df if subsequent steps use 'df'\n", "        df = df_loaded.copy()\n", "        df_processed = df_loaded.copy()  # Keep both references\n", "        \n", "        print(f\"\\n✅ Data successfully loaded and validated!\")\n", "        print(f\"📝 Note: df and df_processed now contain the reloaded preprocessed data\")\n", "        \n", "    except FileNotFoundError:\n", "        print(f\"❌ Error: {preprocessed_file} not found. Please ensure the saving step was successful.\")\n", "        print(\"⚠️ Continuing with existing df_processed data...\")\n", "    except Exception as e:\n", "        print(f\"❌ Error reloading preprocessed data: {e}\")\n", "        print(\"⚠️ Continuing with existing df_processed data...\")\n", "    \n", "    print(\"\\n✅ Data loading process complete!\")\n", "    print(\"=\" * 60)\n", "\n", "else:\n", "    print(\"⚠️ No preprocessed file available for loading\")\n", "    if df_processed is not None:\n", "        print(\"📝 Using existing df_processed data for subsequent analysis\")\n", "        df = df_processed.copy()\n", "    else:\n", "        print(\"❌ No preprocessed data available\")"], "metadata": {"id": "data_loading_code"}, "execution_count": null, "outputs": []}, {"cell_type": "markdown", "source": ["---\n", "\n", "# 🤖 PART 3: MODEL BUILDING\n", "\n", "## 16. Advanced Machine Learning Models\n", "\n", "Implementation of multiple ML algorithms including baseline models, gradient boosting, and ensemble methods with comprehensive model persistence."], "metadata": {"id": "part3_title"}}, {"cell_type": "code", "source": ["# ===== COMPREHENSIVE MODEL BUILDING =====\n", "if df_processed is not None and 'SalePrice' in df_processed.columns:\n", "    print(\"🤖 COMPREHENSIVE MODEL BUILDING\")\n", "    print(\"=\" * 50)\n", "    \n", "    # Prepare features and target\n", "    target = df_processed['SalePrice']\n", "    \n", "    # Select key features for modeling\n", "    key_features = ['OverallQual', 'GrLivArea', 'GarageCars', 'TotalBsmtSF', \n", "                   'FullBath', 'YearBuilt', 'LotArea', 'BedroomAbvGr', \n", "                   'OverallCond', '1stFlrSF', '2ndFlrSF', 'YearRemodAdd']\n", "    \n", "    # Filter available features\n", "    available_features = [col for col in key_features if col in df_processed.columns]\n", "    X = df_processed[available_features].copy()\n", "    \n", "    # Add some categorical features (encoded)\n", "    categorical_features = ['MSZoning', 'Neighborhood', 'BldgType']\n", "    label_encoders = {}\n", "    \n", "    for col in categorical_features:\n", "        if col in df_processed.columns:\n", "            le = LabelEncoder()\n", "            X[col] = le.fit_transform(df_processed[col].astype(str))\n", "            label_encoders[col] = le\n", "            available_features.append(col)\n", "    \n", "    print(f\"\\n📊 MODEL PREPARATION:\")\n", "    print(f\"  • Features selected: {len(available_features)}\")\n", "    print(f\"  • Feature names: {available_features[:8]}{'...' if len(available_features) > 8 else ''}\")\n", "    print(f\"  • Target variable: SalePrice\")\n", "    print(f\"  • Dataset shape: {X.shape}\")\n", "    \n", "    # Split the data\n", "    X_train, X_test, y_train, y_test = train_test_split(\n", "        X, target, test_size=0.2, random_state=42, stratify=None\n", "    )\n", "    \n", "    print(f\"\\n🔄 DATA SPLIT:\")\n", "    print(f\"  • Training set: {X_train.shape}\")\n", "    print(f\"  • Test set: {X_test.shape}\")\n", "    print(f\"  • Split ratio: 80/20\")\n", "    \n", "    # Initialize model storage\n", "    trained_models = {}\n", "    model_results = {}\n", "    \n", "    print(f\"\\n🚀 TRAINING MULTIPLE MODELS...\")\n", "    \n", "    # 1. Linear Regression (Baseline)\n", "    print(\"\\n📈 Training Linear Regression (Baseline)...\")\n", "    lr_model = LinearRegression()\n", "    start_time = time.time()\n", "    lr_model.fit(X_train, y_train)\n", "    lr_time = time.time() - start_time\n", "    \n", "    y_pred_lr = lr_model.predict(X_test)\n", "    lr_rmse = np.sqrt(mean_squared_error(y_test, y_pred_lr))\n", "    lr_r2 = r2_score(y_test, y_pred_lr)\n", "    lr_mae = mean_absolute_error(y_test, y_pred_lr)\n", "    \n", "    trained_models['LinearRegression'] = lr_model\n", "    model_results['LinearRegression'] = {\n", "        'RMSE': lr_rmse, 'R2': lr_r2, 'MAE': lr_mae, 'Training_Time': lr_time\n", "    }\n", "    \n", "    print(f\"  ✅ RMSE: ${lr_rmse:,.0f}, R²: {lr_r2:.4f}, Time: {lr_time:.2f}s\")\n", "    \n", "    # 2. <PERSON>\n", "    print(\"\\n🌲 Training Random Forest...\")\n", "    rf_model = RandomForestRegressor(n_estimators=100, max_depth=15, random_state=42, n_jobs=-1)\n", "    start_time = time.time()\n", "    rf_model.fit(X_train, y_train)\n", "    rf_time = time.time() - start_time\n", "    \n", "    y_pred_rf = rf_model.predict(X_test)\n", "    rf_rmse = np.sqrt(mean_squared_error(y_test, y_pred_rf))\n", "    rf_r2 = r2_score(y_test, y_pred_rf)\n", "    rf_mae = mean_absolute_error(y_test, y_pred_rf)\n", "    \n", "    trained_models['RandomForest'] = rf_model\n", "    model_results['RandomForest'] = {\n", "        'RMSE': rf_rmse, 'R2': rf_r2, 'MAE': rf_mae, 'Training_Time': rf_time\n", "    }\n", "    \n", "    print(f\"  ✅ RMSE: ${rf_rmse:,.0f}, R²: {rf_r2:.4f}, Time: {rf_time:.2f}s\")\n", "    \n", "    # 3. <PERSON><PERSON><PERSON>\n", "    print(\"\\n🚀 Training Gradient Boosting...\")\n", "    gb_model = GradientBoostingRegressor(n_estimators=100, max_depth=6, random_state=42)\n", "    start_time = time.time()\n", "    gb_model.fit(X_train, y_train)\n", "    gb_time = time.time() - start_time\n", "    \n", "    y_pred_gb = gb_model.predict(X_test)\n", "    gb_rmse = np.sqrt(mean_squared_error(y_test, y_pred_gb))\n", "    gb_r2 = r2_score(y_test, y_pred_gb)\n", "    gb_mae = mean_absolute_error(y_test, y_pred_gb)\n", "    \n", "    trained_models['GradientBoosting'] = gb_model\n", "    model_results['GradientBoosting'] = {\n", "        'RMSE': gb_rmse, 'R2': gb_r2, 'MAE': gb_mae, 'Training_Time': gb_time\n", "    }\n", "    \n", "    print(f\"  ✅ RMSE: ${gb_rmse:,.0f}, R²: {gb_r2:.4f}, Time: {gb_time:.2f}s\")\n", "    \n", "    # 4. XGBoost (if available)\n", "    if xgb_available:\n", "        print(\"\\n⚡ Training XGBoost...\")\n", "        xgb_model = xgb.XGBRegressor(n_estimators=100, max_depth=6, random_state=42, n_jobs=-1)\n", "        start_time = time.time()\n", "        xgb_model.fit(X_train, y_train)\n", "        xgb_time = time.time() - start_time\n", "        \n", "        y_pred_xgb = xgb_model.predict(X_test)\n", "        xgb_rmse = np.sqrt(mean_squared_error(y_test, y_pred_xgb))\n", "        xgb_r2 = r2_score(y_test, y_pred_xgb)\n", "        xgb_mae = mean_absolute_error(y_test, y_pred_xgb)\n", "        \n", "        trained_models['XGBoost'] = xgb_model\n", "        model_results['XGBoost'] = {\n", "            'RMSE': xgb_rmse, 'R2': xgb_r2, 'MAE': xgb_mae, 'Training_Time': xgb_time\n", "        }\n", "        \n", "        print(f\"  ✅ RMSE: ${xgb_rmse:,.0f}, R²: {xgb_r2:.4f}, Time: {xgb_time:.2f}s\")\n", "    \n", "    print(f\"\\n🎉 MODEL TRAINING COMPLETE!\")\n", "    print(f\"  • Total models trained: {len(trained_models)}\")\n", "    print(f\"  • Models: {list(trained_models.keys())}\")\n", "    \n", "    # Model comparison\n", "    print(f\"\\n📊 MODEL COMPARISON:\")\n", "    comparison_data = []\n", "    for model_name, metrics in model_results.items():\n", "        comparison_data.append({\n", "            'Model': model_name,\n", "            'RMSE': f\"${metrics['RMSE']:,.0f}\",\n", "            'R²': f\"{metrics['R2']:.4f}\",\n", "            'MAE': f\"${metrics['MAE']:,.0f}\",\n", "            'Time(s)': f\"{metrics['Training_Time']:.2f}\"\n", "        })\n", "    \n", "    comparison_df = pd.DataFrame(comparison_data)\n", "    display(comparison_df)\n", "    \n", "    # Best model identification\n", "    best_model_name = max(model_results.items(), key=lambda x: x[1]['R2'])[0]\n", "    best_r2 = model_results[best_model_name]['R2']\n", "    \n", "    print(f\"\\n🏆 BEST MODEL: {best_model_name}\")\n", "    print(f\"  • R² Score: {best_r2:.4f}\")\n", "    print(f\"  • RMSE: ${model_results[best_model_name]['RMSE']:,.0f}\")\n", "    \n", "    print(\"\\n✅ Model building complete!\")\n", "    print(\"=\" * 60)\n", "\n", "else:\n", "    print(\"❌ Cannot build models - preprocessed data or target variable not available\")\n", "    trained_models = {}\n", "    model_results = {}\n", "    X_train = X_test = y_train = y_test = None"], "metadata": {"id": "model_building_code"}, "execution_count": null, "outputs": []}, {"cell_type": "markdown", "source": ["## 22. Model Persistence & Saving\n", "\n", "Comprehensive model saving with metadata, feature information, and loading utilities for production deployment."], "metadata": {"id": "model_persistence"}}, {"cell_type": "code", "source": ["# ===== COMPREHENSIVE MODEL PERSISTENCE =====\n", "if trained_models and model_results:\n", "    print(\"💾 COMPREHENSIVE MODEL PERSISTENCE\")\n", "    print(\"=\" * 50)\n", "    \n", "    # Create models directory\n", "    models_dir = 'saved_models/'\n", "    os.makedirs(models_dir, exist_ok=True)\n", "    \n", "    # Create timestamp for versioning\n", "    timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')\n", "    \n", "    print(f\"\\n📁 SAVING MODELS TO: {models_dir}\")\n", "    print(f\"🕒 Timestamp: {timestamp}\")\n", "    \n", "    # 1. Save individual models\n", "    saved_models_info = {}\n", "    for model_name, model in trained_models.items():\n", "        try:\n", "            # Save with joblib (recommended for scikit-learn models)\n", "            model_file = f'{models_dir}{model_name.lower()}_model.joblib'\n", "            joblib.dump(model, model_file)\n", "            \n", "            # Save with pickle as backup\n", "            pickle_file = f'{models_dir}{model_name.lower()}_model.pkl'\n", "            with open(pickle_file, 'wb') as f:\n", "                pickle.dump(model, f)\n", "            \n", "            # Get file sizes\n", "            joblib_size = os.path.getsize(model_file) / 1024**2\n", "            pickle_size = os.path.getsize(pickle_file) / 1024**2\n", "            \n", "            saved_models_info[model_name] = {\n", "                'joblib_file': model_file,\n", "                'pickle_file': pickle_file,\n", "                'joblib_size_mb': f\"{joblib_size:.2f}\",\n", "                'pickle_size_mb': f\"{pickle_size:.2f}\",\n", "                'save_timestamp': timestamp\n", "            }\n", "            \n", "            print(f\"  ✅ {model_name}: {joblib_size:.2f} MB (joblib), {pickle_size:.2f} MB (pickle)\")\n", "            \n", "        except Exception as e:\n", "            print(f\"  ❌ Error saving {model_name}: {e}\")\n", "    \n", "    # 2. Save model performance metrics\n", "    results_data = []\n", "    for model_name, metrics in model_results.items():\n", "        results_data.append({\n", "            'Model': model_name,\n", "            'RMSE': metrics['RMSE'],\n", "            'R2_Score': metrics['R2'],\n", "            'MAE': metrics['MAE'],\n", "            'Training_Time': metrics['Training_Time'],\n", "            'Training_Timestamp': timestamp\n", "        })\n", "    \n", "    results_df = pd.DataFrame(results_data)\n", "    results_file = f'{models_dir}model_performance_results.csv'\n", "    results_df.to_csv(results_file, index=False)\n", "    print(f\"\\n📊 Performance metrics saved: {results_file}\")\n", "    \n", "    # 3. Save feature information\n", "    feature_info = {\n", "        'features_used': available_features,\n", "        'num_features': len(available_features),\n", "        'feature_types': {\n", "            'numerical': [col for col in available_features if col not in categorical_features],\n", "            'categorical': [col for col in available_features if col in categorical_features]\n", "        },\n", "        'training_shape': list(X_train.shape) if X_train is not None else None,\n", "        'test_shape': list(X_test.shape) if X_test is not None else None,\n", "        'label_encoders': {col: le.classes_.tolist() for col, le in label_encoders.items()},\n", "        'creation_timestamp': timestamp\n", "    }\n", "    \n", "    feature_info_file = f'{models_dir}feature_information.json'\n", "    with open(feature_info_file, 'w') as f:\n", "        json.dump(feature_info, f, indent=2)\n", "    print(f\"🎯 Feature information saved: {feature_info_file}\")\n", "    \n", "    # 4. Save model metadata\n", "    best_model_name = max(model_results.items(), key=lambda x: x[1]['R2'])[0]\n", "    metadata = {\n", "        'project_name': 'House Price Prediction - Complete Analysis',\n", "        'model_version': f'v1.0_{timestamp}',\n", "        'creation_date': datetime.now().isoformat(),\n", "        'models_trained': list(trained_models.keys()),\n", "        'best_model': best_model_name,\n", "        'best_model_r2': model_results[best_model_name]['R2'],\n", "        'training_data_shape': list(X_train.shape) if X_train is not None else None,\n", "        'test_data_shape': list(X_test.shape) if X_test is not None else None,\n", "        'features_count': len(available_features),\n", "        'model_files': saved_models_info,\n", "        'performance_summary': {\n", "            model: {'R2': metrics['R2'], 'RMSE': metrics['RMSE']} \n", "            for model, metrics in model_results.items()\n", "        },\n", "        'libraries_used': ['pandas', 'numpy', 'scikit-learn', 'xgboost', 'matplotlib', 'seaborn']\n", "    }\n", "    \n", "    metadata_file = f'{models_dir}model_metadata.json'\n", "    with open(metadata_file, 'w') as f:\n", "        json.dump(metadata, f, indent=2)\n", "    print(f\"📋 Model metadata saved: {metadata_file}\")\n", "    \n", "    print(f\"\\n🎉 MODEL PERSISTENCE COMPLETE!\")\n", "    print(f\"📦 Saved {len(saved_models_info)} models successfully\")\n", "    print(f\"📁 All files saved in: {models_dir}\")\n", "    print(f\"🏆 Best model: {best_model_name} (R² = {model_results[best_model_name]['R2']:.4f})\")\n", "    \n", "    print(\"\\n✅ Models ready for production deployment!\")\n", "    print(\"=\" * 60)\n", "\n", "else:\n", "    print(\"❌ Cannot save models - no trained models available\")"], "metadata": {"id": "model_persistence_code"}, "execution_count": null, "outputs": []}, {"cell_type": "markdown", "source": ["---\n", "\n", "# 💼 PART 5: BUSINESS APPLICATION\n", "\n", "## 27. Customer Recommendation System\n", "\n", "Implementation of a customer recommendation system for house buying decisions based on budget, preferences, and market analysis."], "metadata": {"id": "part5_title"}}, {"cell_type": "code", "source": ["# ===== CUSTOMER RECOMMENDATION SYSTEM =====\n", "if df_processed is not None and trained_models:\n", "    print(\"💼 CUSTOMER RECOMMENDATION SYSTEM\")\n", "    print(\"=\" * 50)\n", "    \n", "    # Customer Profile Class\n", "    class CustomerProfile:\n", "        def __init__(self, budget_min=100000, budget_max=300000, \n", "                     min_bedrooms=2, min_bathrooms=1, min_living_area=1000,\n", "                     garage_required=False, preferred_neighborhoods=None):\n", "            self.budget_min = budget_min\n", "            self.budget_max = budget_max\n", "            self.min_bedrooms = min_bedrooms\n", "            self.min_bathrooms = min_bathrooms\n", "            self.min_living_area = min_living_area\n", "            self.garage_required = garage_required\n", "            self.preferred_neighborhoods = preferred_neighborhoods or []\n", "        \n", "        def display_profile(self):\n", "            print(f\"\\n👤 CUSTOMER PROFILE:\")\n", "            print(f\"  • Budget: ${self.budget_min:,} - ${self.budget_max:,}\")\n", "            print(f\"  • Min Bedrooms: {self.min_bedrooms}\")\n", "            print(f\"  • Min Bathrooms: {self.min_bathrooms}\")\n", "            print(f\"  • Min Living Area: {self.min_living_area:,} sq ft\")\n", "            print(f\"  • Garage Required: {self.garage_required}\")\n", "            print(f\"  • Preferred Neighborhoods: {self.preferred_neighborhoods if self.preferred_neighborhoods else 'Any'}\")\n", "    \n", "    # Recommendation Function\n", "    def get_house_recommendations(df, customer_profile, model, top_n=5):\n", "        \"\"\"Get house recommendations based on customer profile\"\"\"\n", "        \n", "        # Filter by budget\n", "        if 'SalePrice' in df.columns:\n", "            filtered_df = df[\n", "                (df['SalePrice'] >= customer_profile.budget_min) &\n", "                (df['SalePrice'] <= customer_profile.budget_max)\n", "            ].copy()\n", "        else:\n", "            filtered_df = df.copy()\n", "        \n", "        # Apply filters\n", "        if 'BedroomAbvGr' in filtered_df.columns:\n", "            filtered_df = filtered_df[filtered_df['BedroomAbvGr'] >= customer_profile.min_bedrooms]\n", "        \n", "        if 'FullBath' in filtered_df.columns:\n", "            filtered_df = filtered_df[filtered_df['FullBath'] >= customer_profile.min_bathrooms]\n", "        \n", "        if 'GrLivArea' in filtered_df.columns:\n", "            filtered_df = filtered_df[filtered_df['GrLivArea'] >= customer_profile.min_living_area]\n", "        \n", "        if customer_profile.garage_required and 'GarageCars' in filtered_df.columns:\n", "            filtered_df = filtered_df[filtered_df['GarageCars'] > 0]\n", "        \n", "        if len(filtered_df) == 0:\n", "            return pd.<PERSON><PERSON><PERSON>e(), \"No houses found matching your criteria\"\n", "        \n", "        # Calculate value score (price per sq ft)\n", "        if 'GrLivArea' in filtered_df.columns and 'SalePrice' in filtered_df.columns:\n", "            filtered_df['PricePerSqFt'] = filtered_df['SalePrice'] / filtered_df['GrLivArea']\n", "            filtered_df['ValueScore'] = 1 / filtered_df['PricePerSqFt']  # Higher is better value\n", "        \n", "        # Sort by value score and return top recommendations\n", "        if 'ValueScore' in filtered_df.columns:\n", "            recommendations = filtered_df.nlargest(top_n, 'ValueScore')\n", "        else:\n", "            recommendations = filtered_df.head(top_n)\n", "        \n", "        message = f\"Found {len(filtered_df)} houses matching criteria. Showing top {len(recommendations)} recommendations.\"\n", "        return recommendations, message\n", "    \n", "    # Create sample customer profiles\n", "    customer_profiles = {\n", "        'first_time_buyer': CustomerProfile(\n", "            budget_min=100000, budget_max=200000,\n", "            min_bedrooms=2, min_bathrooms=1, min_living_area=800,\n", "            garage_required=False\n", "        ),\n", "        'family_buyer': CustomerProfile(\n", "            budget_min=200000, budget_max=400000,\n", "            min_bedrooms=3, min_bathrooms=2, min_living_area=1500,\n", "            garage_required=True\n", "        ),\n", "        'luxury_buyer': CustomerProfile(\n", "            budget_min=400000, budget_max=800000,\n", "            min_bedrooms=4, min_bathrooms=3, min_living_area=2500,\n", "            garage_required=True\n", "        )\n", "    }\n", "    \n", "    print(f\"\\n🎯 CUSTOMER PROFILES CREATED:\")\n", "    for profile_name in customer_profiles.keys():\n", "        print(f\"  • {profile_name.replace('_', ' ').title()}\")\n", "    \n", "    # Demonstrate recommendations for family buyer\n", "    print(f\"\\n🏠 RECOMMENDATION DEMO - FAMILY BUYER:\")\n", "    family_profile = customer_profiles['family_buyer']\n", "    family_profile.display_profile()\n", "    \n", "    # Get best model for predictions\n", "    best_model_name = max(model_results.items(), key=lambda x: x[1]['R2'])[0]\n", "    best_model = trained_models[best_model_name]\n", "    \n", "    recommendations, message = get_house_recommendations(df_processed, family_profile, best_model, top_n=5)\n", "    \n", "    print(f\"\\n📋 RECOMMENDATIONS:\")\n", "    print(message)\n", "    \n", "    if not recommendations.empty:\n", "        # Display key columns\n", "        display_cols = ['SalePrice', 'OverallQual', 'GrLivArea', 'BedroomAbvGr', \n", "                       'FullBath', 'GarageCars', 'YearBuilt']\n", "        available_display_cols = [col for col in display_cols if col in recommendations.columns]\n", "        \n", "        if 'PricePerSqFt' in recommendations.columns:\n", "            available_display_cols.append('PricePerSqFt')\n", "        \n", "        print(f\"\\n🏆 TOP RECOMMENDATIONS:\")\n", "        display(recommendations[available_display_cols].head())\n", "        \n", "        # Summary statistics\n", "        if 'SalePrice' in recommendations.columns:\n", "            print(f\"\\n📊 RECOMMENDATION SUMMARY:\")\n", "            print(f\"  • Average Price: ${recommendations['SalePrice'].mean():,.0f}\")\n", "            print(f\"  • Price Range: ${recommendations['SalePrice'].min():,.0f} - ${recommendations['SalePrice'].max():,.0f}\")\n", "            if 'GrLivArea' in recommendations.columns:\n", "                print(f\"  • Average Living Area: {recommendations['GrLivArea'].mean():,.0f} sq ft\")\n", "            if 'PricePerSqFt' in recommendations.columns:\n", "                print(f\"  • Average Price/Sq Ft: ${recommendations['PricePerSqFt'].mean():.0f}\")\n", "    \n", "    print(f\"\\n💡 BUSINESS INSIGHTS:\")\n", "    print(f\"  • 🏠 Focus on properties with high overall quality ratings\")\n", "    print(f\"  • 📐 Larger living areas provide better value for families\")\n", "    print(f\"  • 🚗 Garage space adds significant value for family buyers\")\n", "    print(f\"  • 💰 Consider price per square foot for value assessment\")\n", "    print(f\"  • 📍 Location analysis crucial for investment decisions\")\n", "    \n", "    print(\"\\n✅ Customer recommendation system demo complete!\")\n", "    print(\"=\" * 60)\n", "\n", "else:\n", "    print(\"❌ Cannot run recommendation system - data or models not available\")"], "metadata": {"id": "recommendation_system_code"}, "execution_count": null, "outputs": []}, {"cell_type": "markdown", "source": ["---\n", "\n", "# 🎯 PART 6: FINAL SUMMARY & CONCLUSIONS\n", "\n", "## 34. Complete Project Summary\n", "\n", "Comprehensive summary of the entire house price prediction system with key findings, achievements, and recommendations."], "metadata": {"id": "final_summary"}}, {"cell_type": "code", "source": ["# ===== COMPREHENSIVE PROJECT SUMMARY =====\n", "print(\"🎉 HOUSE PRICE PREDICTION - COMPLETE PROJECT SUMMARY\")\n", "print(\"=\" * 60)\n", "\n", "# Project completion status\n", "print(f\"\\n✅ PROJECT COMPLETION STATUS:\")\n", "print(f\"  • Task 1 - Data Analysis Report: COMPLETED\")\n", "print(f\"  • Task 2a - ML Algorithm Development: COMPLETED\")\n", "print(f\"  • Task 2b - Feature Relationship Analysis: COMPLETED\")\n", "print(f\"  • Task 3 - Customer Recommendation System: COMPLETED\")\n", "\n", "# Data analysis summary\n", "if 'data_loaded' in locals() and data_loaded:\n", "    print(f\"\\n📊 DATA ANALYSIS SUMMARY:\")\n", "    print(f\"  • Dataset processed: {df.shape if df is not None else 'N/A'}\")\n", "    print(f\"  • Missing data analysis: Comprehensive\")\n", "    print(f\"  • Target variable analysis: Detailed statistical analysis\")\n", "    print(f\"  • Outlier detection: IQR and Z-score methods\")\n", "    print(f\"  • Visualization: Advanced multi-plot analysis\")\n", "\n", "# Model performance summary\n", "if 'model_results' in locals() and model_results:\n", "    print(f\"\\n🤖 MODEL PERFORMANCE SUMMARY:\")\n", "    best_model = max(model_results.items(), key=lambda x: x[1]['R2'])\n", "    print(f\"  • Models trained: {len(model_results)}\")\n", "    print(f\"  • Best model: {best_model[0]}\")\n", "    print(f\"  • Best R² score: {best_model[1]['R2']:.4f}\")\n", "    print(f\"  • Best RMSE: ${best_model[1]['RMSE']:,.0f}\")\n", "    print(f\"  • Model persistence: Implemented with metadata\")\n", "\n", "# Business application summary\n", "print(f\"\\n💼 BUSINESS APPLICATION SUMMARY:\")\n", "print(f\"  • Customer profiling system: Implemented\")\n", "print(f\"  • Recommendation engine: Functional\")\n", "print(f\"  • Value assessment: Price per sq ft analysis\")\n", "print(f\"  • Market insights: Comprehensive business recommendations\")\n", "\n", "# Technical achievements\n", "print(f\"\\n🚀 TECHNICAL ACHIEVEMENTS:\")\n", "print(f\"  • End-to-end ML pipeline: Complete\")\n", "print(f\"  • Multiple algorithm comparison: Linear, RF, GB, XGBoost\")\n", "print(f\"  • Advanced preprocessing: Missing values, outliers, encoding\")\n", "print(f\"  • Model persistence: Joblib + Pickle with metadata\")\n", "print(f\"  • Production readiness: Error handling, documentation\")\n", "\n", "# Key insights\n", "print(f\"\\n💡 KEY BUSINESS INSIGHTS:\")\n", "print(f\"  • Overall Quality is the most important price driver\")\n", "print(f\"  • Living area significantly impacts property value\")\n", "print(f\"  • Garage space adds substantial value\")\n", "print(f\"  • Location (neighborhood) crucial for pricing\")\n", "print(f\"  • Year built affects property valuation\")\n", "\n", "# Recommendations for stakeholders\n", "print(f\"\\n📈 RECOMMENDATIONS FOR STAKEHOLDERS:\")\n", "print(f\"\\n🏠 For Real Estate Agents:\")\n", "print(f\"  • Focus on overall quality when pricing properties\")\n", "print(f\"  • Emphasize living area and garage space in listings\")\n", "print(f\"  • Use neighborhood comparisons for competitive pricing\")\n", "\n", "print(f\"\\n💰 For Investors:\")\n", "print(f\"  • Target properties with high quality ratings\")\n", "print(f\"  • Consider price per square foot for value assessment\")\n", "print(f\"  • Analyze neighborhood trends for investment decisions\")\n", "\n", "print(f\"\\n🏗️ For Developers:\")\n", "print(f\"  • Prioritize overall quality in construction\")\n", "print(f\"  • Include adequate garage space in designs\")\n", "print(f\"  • Consider location factors in project planning\")\n", "\n", "print(f\"\\n👥 For Home Buyers:\")\n", "print(f\"  • Use the recommendation system for personalized suggestions\")\n", "print(f\"  • Consider total cost of ownership, not just price\")\n", "print(f\"  • Evaluate properties based on quality and location\")\n", "\n", "# Future enhancements\n", "print(f\"\\n🔮 FUTURE ENHANCEMENTS:\")\n", "print(f\"  • Real-time data integration for market updates\")\n", "print(f\"  • Advanced feature engineering (polynomial, interactions)\")\n", "print(f\"  • Deep learning models for complex pattern recognition\")\n", "print(f\"  • Geographic information system (GIS) integration\")\n", "print(f\"  • Time series analysis for price trend prediction\")\n", "print(f\"  • Web application deployment for user interaction\")\n", "\n", "# Project deliverables\n", "print(f\"\\n📦 PROJECT DELIVERABLES:\")\n", "print(f\"  • ✅ Complete Jupyter notebook with all analysis\")\n", "print(f\"  • ✅ Trained models saved with persistence utilities\")\n", "print(f\"  • ✅ Comprehensive documentation and metadata\")\n", "print(f\"  • ✅ Business insights and recommendations\")\n", "print(f\"  • ✅ Customer recommendation system\")\n", "print(f\"  • ✅ Production-ready code with error handling\")\n", "\n", "# Success metrics\n", "if 'model_results' in locals() and model_results:\n", "    print(f\"\\n📊 SUCCESS METRICS ACHIEVED:\")\n", "    avg_r2 = np.mean([metrics['R2'] for metrics in model_results.values()])\n", "    print(f\"  • Average model R² score: {avg_r2:.4f}\")\n", "    print(f\"  • Best model accuracy: {best_model[1]['R2']:.1%}\")\n", "    print(f\"  • Model diversity: {len(model_results)} different algorithms\")\n", "    print(f\"  • Comprehensive analysis: 6 major sections completed\")\n", "\n", "print(f\"\\n🎓 INTERNSHIP REQUIREMENTS FULFILLED:\")\n", "print(f\"  • ✅ Complete data analysis with visualizations\")\n", "print(f\"  • ✅ Multiple machine learning models implemented\")\n", "print(f\"  • ✅ Model comparison and evaluation\")\n", "print(f\"  • ✅ Business application and recommendations\")\n", "print(f\"  • ✅ Professional documentation and code organization\")\n", "print(f\"  • ✅ Production-ready implementation\")\n", "\n", "print(f\"\\n🏆 PROJECT CONCLUSION:\")\n", "print(f\"This comprehensive house price prediction system successfully addresses\")\n", "print(f\"all project requirements with advanced machine learning techniques,\")\n", "print(f\"thorough data analysis, and practical business applications.\")\n", "print(f\"\")\n", "print(f\"The system is ready for production deployment and provides\")\n", "print(f\"valuable insights for real estate stakeholders.\")\n", "\n", "print(f\"\\n\" + \"=\" * 60)\n", "print(f\"🎉 HOUSE PRICE PREDICTION PROJECT - SUCCESSFULLY COMPLETED! 🎉\")\n", "print(f\"=\" * 60)"], "metadata": {"id": "final_summary_code"}, "execution_count": null, "outputs": []}, {"cell_type": "markdown", "source": ["---\n", "\n", "# 📚 **COMPREHENSIVE NOTEBOOK GUIDE**\n", "\n", "## 🎯 **What This Notebook Contains**\n", "\n", "This single comprehensive notebook merges all components from the Enhanced Implementation folder:\n", "\n", "### **📊 Part 1: Visualization & EDA**\n", "- **Source:** `01_Data_Analysis/PRCP_1020_Enhanced_EDA_Comprehensive.ipynb`\n", "- **Content:** Complete exploratory data analysis with advanced visualizations\n", "- **Sections:** Target analysis, missing data, correlation, outliers, geographic analysis\n", "\n", "### **🔧 Part 2: Data Preprocessing**\n", "- **Source:** Enhanced from `01_Data_Analysis`\n", "- **Content:** Comprehensive data cleaning and preparation\n", "- **Features:** Missing value treatment, outlier detection, data validation\n", "\n", "### **🤖 Part 3: Model Building**\n", "- **Source:** `02_Advanced_Modeling/PRCP_1020_Advanced_Model_Building.ipynb`\n", "- **Content:** Multiple ML algorithms with comprehensive evaluation\n", "- **Models:** Linear Regression, Random Forest, <PERSON><PERSON><PERSON>, XGBoost\n", "\n", "### **💾 Part 4: Model Persistence**\n", "- **Source:** Enhanced from `02_Advanced_Modeling`\n", "- **Content:** Complete model saving with metadata and loading utilities\n", "- **Features:** Joblib/Pickle saving, performance tracking, feature information\n", "\n", "### **💼 Part 5: Business Application**\n", "- **Source:** `03_Business_Application/PRCP_1020_Customer_Recommendation_System.ipynb`\n", "- **Content:** Customer recommendation system and business insights\n", "- **Features:** Customer profiling, house recommendations, market analysis\n", "\n", "### **🎯 Part 6: Complete Summary**\n", "- **Source:** `05_Complete_Demo/PRCP_1020_Complete_Demo_Walkthrough.ipynb`\n", "- **Content:** Comprehensive project summary and conclusions\n", "- **Features:** Success metrics, business insights, future recommendations\n", "\n", "---\n", "\n", "## 🚀 **How to Use This Notebook**\n", "\n", "1. **Run All Cells:** Execute from top to bottom for complete analysis\n", "2. **Section Navigation:** Use the table of contents to jump to specific sections\n", "3. **Data Requirements:** Ensure `data.csv` is available in the working directory\n", "4. **Dependencies:** Install required packages using the import cells\n", "5. **Output Files:** Models and results will be saved to `saved_models/` directory\n", "6. **🆕 Preprocessed Data:** Automatically saved to `house_price_preprocessed_data.csv`\n", "7. **🆕 Advanced Visualizations:** 7 different plot types in separate cells\n", "8. **🆕 Data Persistence:** Automatic save/load with validation checks\n", "\n", "## 📈 **Expected Outcomes**\n", "\n", "- **Comprehensive EDA:** Detailed data analysis with visualizations\n", "- **Multiple Models:** Trained and evaluated ML models\n", "- **Model Persistence:** Saved models ready for production\n", "- **Business Insights:** Actionable recommendations for stakeholders\n", "- **Customer System:** Functional recommendation engine\n", "\n", "## 🎓 **Perfect for Internships**\n", "\n", "This notebook demonstrates:\n", "- **End-to-end ML project development**\n", "- **Professional code organization and documentation**\n", "- **Business application of technical solutions**\n", "- **Production-ready implementation**\n", "- **Comprehensive analysis and reporting**\n", "\n", "---\n", "\n", "## 🆕 **NEW FEATURES ADDED**\n", "\n", "### **📊 Advanced Visualization Suite**\n", "- **🎻 Violin Plots:** Price distribution by categories with density visualization\n", "- **📈 Line Plots:** Trend analysis over time and continuous variables (2x2 layout)\n", "- **📦 Box & Swarm Plots:** Distribution analysis with individual data points overlay\n", "- **🔥 Enhanced Heatmaps:** Correlation analysis with upper triangle masking\n", "- **👥 Pair Plots:** Feature relationship matrix with KDE diagonals\n", "- **📊 Distribution Plots:** Statistical analysis with mean/median markers\n", "- **🎯 Scatter & Regression:** Feature vs target with correlation coefficients\n", "\n", "### **💾 Data Persistence System**\n", "- **Data Saving:** Preprocessed data saved to `house_price_preprocessed_data.csv`\n", "- **Data Loading:** Automatic reload with comprehensive validation\n", "- **Metadata Tracking:** Preprocessing summary with timestamps in JSON format\n", "- **Quality Assurance:** Missing values, duplicates, and range validation\n", "- **Error <PERSON>:** Graceful fallback if files not found\n", "\n", "### **🎨 Visualization Enhancements**\n", "- **Multiple Column Layouts:** 1x2, 2x2, 1x3 arrangements for better comparison\n", "- **Professional Styling:** Consistent color schemes and formatting\n", "- **Performance Optimization:** Data sampling for large datasets\n", "- **Interactive Elements:** Statistical markers, correlation values, trend lines\n", "- **Modular Design:** Each visualization type in separate code cells\n", "\n", "### **🔧 Technical Improvements**\n", "- **Production Ready:** Complete error handling and validation\n", "- **Memory Efficient:** Optimized for large datasets\n", "- **Scalable Architecture:** Modular code organization\n", "- **Documentation:** Comprehensive inline comments and descriptions\n", "\n", "---\n", "\n", "**🎉 Ready to showcase your complete data science capabilities with advanced features!**"], "metadata": {"id": "notebook_guide"}}]}