{"nbformat": 4, "nbformat_minor": 0, "metadata": {"colab": {"provenance": [], "gpuType": "V28"}, "kernelspec": {"name": "python3", "display_name": "Python 3"}, "language_info": {"name": "python"}, "accelerator": "TPU"}, "cells": [{"cell_type": "markdown", "source": ["\n", "\n", "# PRCP-1020-HousePricePred - Data Processing & Model Building\n", "\n", "# Problem Statement\n", "\n", "Task 1:- Prepare a complete data analysis report on the given data.\n", "\n", "Task 2:-a) Create a robust machine learning algorithm to accurately predict the price of the house given the various factors across the market.      \n", "            b) Determine the relationship between the house features and how the price varies based on this.\n", "\n", "Task3:- Come up with suggestions for the customer to buy the house according to the area, price and other requirements.\n", "\n", "\n", "# Dataset Description and Link:\n", "Ask a home buyer to describe their dream house, and they probably won't begin with the height of the basement ceiling or the proximity to an east-west railroad. But this playground competition's dataset proves that much more influences price negotiations than the number of bedrooms or a white-picket fence.\n", "With 79 explanatory variables describing (almost) every aspect of residential homes in Ames, Iowa, this competition challenges you to predict the final price of each home.\n", "# Practice Skills\n", "●\tCreative feature engineering.\n", "\n", "●\tAdvanced regression techniques like random forest and gradient boosting.\n", "\n", "\n", "Link : https://d3ilbtxij3aepc.cloudfront.net/projects/CDS-Capstone-Projects/PRCP-1020-HousePricePred.zip\n", "\n", "\n", "\n"], "metadata": {"id": "7qsz1GqXqA6F"}}, {"cell_type": "code", "execution_count": 71, "metadata": {"id": "-t2Vpb02pnsY"}, "outputs": [], "source": ["import pandas as pd\n", "import numpy as np\n", "import matplotlib.pyplot as plt\n", "import seaborn as sns\n", "from sklearn.model_selection import train_test_split\n", "from sklearn.preprocessing import StandardScaler, OneHotEncoder\n", "from sklearn.impute import SimpleImputer\n", "from sklearn.compose import ColumnTransformer\n", "from sklearn.pipeline import Pipeline\n", "from sklearn.ensemble import RandomForestRegressor\n", "from sklearn.linear_model import LassoCV\n", "from sklearn.metrics import mean_squared_error, r2_score\n", "import warnings\n", "\n", "# Suppress warnings for cleaner output\n", "warnings.filterwarnings(\"ignore\")\n", "\n", "# Display options\n", "pd.set_option(\"display.max_columns\", None)\n", "%matplotlib inline"]}, {"cell_type": "code", "source": ["# Load the dataset\n", "try:\n", "    df = pd.read_csv(\"data.csv\")\n", "    # Display first few rows\n", "    print(\"First 5 rows of raw data:\")\n", "    try:\n", "        from IPython.display import display\n", "        display(df.head())\n", "    except ImportError:\n", "        print(df.head())\n", "except FileNotFoundError:\n", "    print(\"Error: data.csv not found. Please ensure the file is uploaded or in the correct directory.\")\n", "    # Exit if data not loaded\n", "    exit()"], "metadata": {"colab": {"base_uri": "https://localhost:8080/", "height": 244}, "id": "TjipaHJUp-w9", "outputId": "93df396b-e0b2-4ee8-ae65-98ea5989fbdf"}, "execution_count": 72, "outputs": [{"output_type": "stream", "name": "stdout", "text": ["First 5 rows of raw data:\n"]}, {"output_type": "display_data", "data": {"text/plain": ["   Id  MSSubClass MSZoning  LotFrontage  LotArea Street Alley LotShape  \\\n", "0   1          60       RL         65.0     8450   Pave   NaN      Reg   \n", "1   2          20       RL         80.0     9600   Pave   NaN      Reg   \n", "2   3          60       RL         68.0    11250   Pave   NaN      IR1   \n", "3   4          70       RL         60.0     9550   Pave   NaN      IR1   \n", "4   5          60       RL         84.0    14260   Pave   NaN      IR1   \n", "\n", "  LandContour Utilities LotConfig LandSlope Neighborhood Condition1  \\\n", "0         Lvl    AllPub    Inside       Gtl      CollgCr       Norm   \n", "1         Lvl    AllPub       FR2       Gtl      Veenker      Feedr   \n", "2         Lvl    AllPub    Inside       Gtl      CollgCr       Norm   \n", "3         Lvl    AllPub    Corner       Gtl      Crawfor       Norm   \n", "4         Lvl    AllPub       FR2       Gtl      NoRidge       Norm   \n", "\n", "  Condition2 BldgType HouseStyle  OverallQual  OverallCond  YearBuilt  \\\n", "0       Norm     1Fam     2Story            7            5       2003   \n", "1       Norm     1Fam     1Story            6            8       1976   \n", "2       Norm     1Fam     2Story            7            5       2001   \n", "3       Norm     1Fam     2Story            7            5       1915   \n", "4       Norm     1Fam     2Story            8            5       2000   \n", "\n", "   YearRemodAdd RoofStyle RoofMatl Exterior1st Exterior2nd MasVnrType  \\\n", "0          2003     Gable  CompShg     VinylSd     VinylSd    BrkFace   \n", "1          1976     Gable  CompShg     MetalSd     MetalSd        NaN   \n", "2          2002     Gable  CompShg     VinylSd     VinylSd    BrkFace   \n", "3          1970     Gable  CompShg     Wd Sdng     Wd Shng        NaN   \n", "4          2000     Gable  CompShg     VinylSd     VinylSd    BrkFace   \n", "\n", "   MasVnrArea ExterQual ExterCond Foundation BsmtQual BsmtCond BsmtExposure  \\\n", "0       196.0        Gd        TA      PConc       Gd       TA           No   \n", "1         0.0        TA        TA     CBlock       Gd       TA           Gd   \n", "2       162.0        Gd        TA      PConc       Gd       TA           Mn   \n", "3         0.0        TA        TA     BrkTil       TA       Gd           No   \n", "4       350.0        Gd        TA      PConc       Gd       TA           Av   \n", "\n", "  BsmtFinType1  BsmtFinSF1 BsmtFinType2  BsmtFinSF2  BsmtUnfSF  TotalBsmtSF  \\\n", "0          GLQ         706          Unf           0        150          856   \n", "1          ALQ         978          Unf           0        284         1262   \n", "2          GLQ         486          Unf           0        434          920   \n", "3          ALQ         216          Unf           0        540          756   \n", "4          GLQ         655          Unf           0        490         1145   \n", "\n", "  Heating HeatingQC CentralAir Electrical  1stFlrSF  2ndFlrSF  LowQualFinSF  \\\n", "0    GasA        Ex          Y      SBrkr       856       854             0   \n", "1    GasA        Ex          Y      SBrkr      1262         0             0   \n", "2    GasA        Ex          Y      SBrkr       920       866             0   \n", "3    GasA        Gd          Y      SBrkr       961       756             0   \n", "4    GasA        Ex          Y      SBrkr      1145      1053             0   \n", "\n", "   GrLivArea  BsmtFullBath  BsmtHalfBath  FullBath  HalfBath  BedroomAbvGr  \\\n", "0       1710             1             0         2         1             3   \n", "1       1262             0             1         2         0             3   \n", "2       1786             1             0         2         1             3   \n", "3       1717             1             0         1         0             3   \n", "4       2198             1             0         2         1             4   \n", "\n", "   KitchenAbvGr KitchenQual  TotRmsAbvGrd Functional  Fireplaces FireplaceQu  \\\n", "0             1          Gd             8        Typ           0         NaN   \n", "1             1          TA             6        Typ           1          TA   \n", "2             1          Gd             6        Typ           1          TA   \n", "3             1          Gd             7        Typ           1          Gd   \n", "4             1          Gd             9        Typ           1          TA   \n", "\n", "  GarageType  GarageYrBlt GarageFinish  GarageCars  GarageArea GarageQual  \\\n", "0     Attchd       2003.0          RFn           2         548         TA   \n", "1     Attchd       1976.0          RFn           2         460         TA   \n", "2     Attchd       2001.0          RFn           2         608         TA   \n", "3     Detchd       1998.0          Unf           3         642         TA   \n", "4     Attchd       2000.0          RFn           3         836         TA   \n", "\n", "  GarageCond PavedDrive  WoodDeckSF  OpenPorchSF  EnclosedPorch  3SsnPorch  \\\n", "0         TA          Y           0           61              0          0   \n", "1         TA          Y         298            0              0          0   \n", "2         TA          Y           0           42              0          0   \n", "3         TA          Y           0           35            272          0   \n", "4         TA          Y         192           84              0          0   \n", "\n", "   ScreenPorch  PoolArea PoolQC Fence MiscFeature  MiscVal  MoSold  YrSold  \\\n", "0            0         0    NaN   NaN         NaN        0       2    2008   \n", "1            0         0    NaN   NaN         NaN        0       5    2007   \n", "2            0         0    NaN   NaN         NaN        0       9    2008   \n", "3            0         0    NaN   NaN         NaN        0       2    2006   \n", "4            0         0    NaN   NaN         NaN        0      12    2008   \n", "\n", "  SaleType SaleCondition  SalePrice  \n", "0       WD        Normal     208500  \n", "1       WD        Normal     181500  \n", "2       WD        Normal     223500  \n", "3       WD       Abnorml     140000  \n", "4       WD        Normal     250000  "], "text/html": ["\n", "  <div id=\"df-2b8613a6-2f7f-4105-b2cc-461820f319bd\" class=\"colab-df-container\">\n", "    <div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>Id</th>\n", "      <th>MSSubClass</th>\n", "      <th>MSZoning</th>\n", "      <th>LotFrontage</th>\n", "      <th>LotArea</th>\n", "      <th>Street</th>\n", "      <th>Alley</th>\n", "      <th><PERSON><PERSON><PERSON><PERSON></th>\n", "      <th>LandContour</th>\n", "      <th>Utilities</th>\n", "      <th>LotConfig</th>\n", "      <th>LandSlope</th>\n", "      <th>Neighborhood</th>\n", "      <th>Condition1</th>\n", "      <th>Condition2</th>\n", "      <th>BldgType</th>\n", "      <th>HouseStyle</th>\n", "      <th>OverallQual</th>\n", "      <th>OverallCond</th>\n", "      <th>YearBuilt</th>\n", "      <th>YearRemodAdd</th>\n", "      <th>RoofStyle</th>\n", "      <th>RoofMatl</th>\n", "      <th>Exterior1st</th>\n", "      <th>Exterior2nd</th>\n", "      <th>MasVnrType</th>\n", "      <th>MasVnrArea</th>\n", "      <th>ExterQual</th>\n", "      <th>ExterCond</th>\n", "      <th>Foundation</th>\n", "      <th>BsmtQual</th>\n", "      <th>BsmtCond</th>\n", "      <th>BsmtExposure</th>\n", "      <th>BsmtFinType1</th>\n", "      <th>BsmtFinSF1</th>\n", "      <th>BsmtFinType2</th>\n", "      <th>BsmtFinSF2</th>\n", "      <th>BsmtUnfSF</th>\n", "      <th>TotalBsmtSF</th>\n", "      <th>Heating</th>\n", "      <th>HeatingQC</th>\n", "      <th>CentralAir</th>\n", "      <th>Electrical</th>\n", "      <th>1stFlrSF</th>\n", "      <th>2ndFlrSF</th>\n", "      <th>LowQualFinSF</th>\n", "      <th>GrLivArea</th>\n", "      <th>BsmtFullBath</th>\n", "      <th>BsmtHalfBath</th>\n", "      <th>FullBath</th>\n", "      <th>HalfBath</th>\n", "      <th>BedroomAbvGr</th>\n", "      <th>KitchenAbvGr</th>\n", "      <th>KitchenQual</th>\n", "      <th>TotRmsAbvGrd</th>\n", "      <th>Functional</th>\n", "      <th>Fireplaces</th>\n", "      <th>FireplaceQu</th>\n", "      <th>GarageType</th>\n", "      <th>GarageYrBlt</th>\n", "      <th>GarageFinish</th>\n", "      <th>GarageCars</th>\n", "      <th>GarageArea</th>\n", "      <th>GarageQual</th>\n", "      <th>GarageCond</th>\n", "      <th>PavedDrive</th>\n", "      <th>WoodDeckSF</th>\n", "      <th>OpenPorchSF</th>\n", "      <th>EnclosedPorch</th>\n", "      <th>3SsnPorch</th>\n", "      <th>ScreenPorch</th>\n", "      <th>PoolArea</th>\n", "      <th>PoolQC</th>\n", "      <th><PERSON><PERSON></th>\n", "      <th>MiscFeature</th>\n", "      <th>MiscVal</th>\n", "      <th><PERSON><PERSON><PERSON></th>\n", "      <th>YrSold</th>\n", "      <th>SaleType</th>\n", "      <th>SaleCondition</th>\n", "      <th>SalePrice</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>1</td>\n", "      <td>60</td>\n", "      <td>RL</td>\n", "      <td>65.0</td>\n", "      <td>8450</td>\n", "      <td>Pave</td>\n", "      <td>NaN</td>\n", "      <td>Reg</td>\n", "      <td>Lvl</td>\n", "      <td>AllPub</td>\n", "      <td>Inside</td>\n", "      <td>Gtl</td>\n", "      <td>CollgCr</td>\n", "      <td>Norm</td>\n", "      <td>Norm</td>\n", "      <td>1Fam</td>\n", "      <td>2Story</td>\n", "      <td>7</td>\n", "      <td>5</td>\n", "      <td>2003</td>\n", "      <td>2003</td>\n", "      <td>Gable</td>\n", "      <td>CompShg</td>\n", "      <td>VinylSd</td>\n", "      <td>VinylSd</td>\n", "      <td>BrkFace</td>\n", "      <td>196.0</td>\n", "      <td>Gd</td>\n", "      <td>TA</td>\n", "      <td>PConc</td>\n", "      <td>Gd</td>\n", "      <td>TA</td>\n", "      <td>No</td>\n", "      <td>GLQ</td>\n", "      <td>706</td>\n", "      <td>Unf</td>\n", "      <td>0</td>\n", "      <td>150</td>\n", "      <td>856</td>\n", "      <td>GasA</td>\n", "      <td>Ex</td>\n", "      <td>Y</td>\n", "      <td>SBrkr</td>\n", "      <td>856</td>\n", "      <td>854</td>\n", "      <td>0</td>\n", "      <td>1710</td>\n", "      <td>1</td>\n", "      <td>0</td>\n", "      <td>2</td>\n", "      <td>1</td>\n", "      <td>3</td>\n", "      <td>1</td>\n", "      <td>Gd</td>\n", "      <td>8</td>\n", "      <td>Typ</td>\n", "      <td>0</td>\n", "      <td>NaN</td>\n", "      <td>Attchd</td>\n", "      <td>2003.0</td>\n", "      <td>RFn</td>\n", "      <td>2</td>\n", "      <td>548</td>\n", "      <td>TA</td>\n", "      <td>TA</td>\n", "      <td>Y</td>\n", "      <td>0</td>\n", "      <td>61</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>0</td>\n", "      <td>2</td>\n", "      <td>2008</td>\n", "      <td>WD</td>\n", "      <td>Normal</td>\n", "      <td>208500</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>2</td>\n", "      <td>20</td>\n", "      <td>RL</td>\n", "      <td>80.0</td>\n", "      <td>9600</td>\n", "      <td>Pave</td>\n", "      <td>NaN</td>\n", "      <td>Reg</td>\n", "      <td>Lvl</td>\n", "      <td>AllPub</td>\n", "      <td>FR2</td>\n", "      <td>Gtl</td>\n", "      <td><PERSON><PERSON><PERSON></td>\n", "      <td><PERSON>edr</td>\n", "      <td>Norm</td>\n", "      <td>1Fam</td>\n", "      <td>1Story</td>\n", "      <td>6</td>\n", "      <td>8</td>\n", "      <td>1976</td>\n", "      <td>1976</td>\n", "      <td>Gable</td>\n", "      <td>CompShg</td>\n", "      <td>MetalSd</td>\n", "      <td>MetalSd</td>\n", "      <td>NaN</td>\n", "      <td>0.0</td>\n", "      <td>TA</td>\n", "      <td>TA</td>\n", "      <td>CBlock</td>\n", "      <td>Gd</td>\n", "      <td>TA</td>\n", "      <td>Gd</td>\n", "      <td>ALQ</td>\n", "      <td>978</td>\n", "      <td>Unf</td>\n", "      <td>0</td>\n", "      <td>284</td>\n", "      <td>1262</td>\n", "      <td>GasA</td>\n", "      <td>Ex</td>\n", "      <td>Y</td>\n", "      <td>SBrkr</td>\n", "      <td>1262</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>1262</td>\n", "      <td>0</td>\n", "      <td>1</td>\n", "      <td>2</td>\n", "      <td>0</td>\n", "      <td>3</td>\n", "      <td>1</td>\n", "      <td>TA</td>\n", "      <td>6</td>\n", "      <td>Typ</td>\n", "      <td>1</td>\n", "      <td>TA</td>\n", "      <td>Attchd</td>\n", "      <td>1976.0</td>\n", "      <td>RFn</td>\n", "      <td>2</td>\n", "      <td>460</td>\n", "      <td>TA</td>\n", "      <td>TA</td>\n", "      <td>Y</td>\n", "      <td>298</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>0</td>\n", "      <td>5</td>\n", "      <td>2007</td>\n", "      <td>WD</td>\n", "      <td>Normal</td>\n", "      <td>181500</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>3</td>\n", "      <td>60</td>\n", "      <td>RL</td>\n", "      <td>68.0</td>\n", "      <td>11250</td>\n", "      <td>Pave</td>\n", "      <td>NaN</td>\n", "      <td>IR1</td>\n", "      <td>Lvl</td>\n", "      <td>AllPub</td>\n", "      <td>Inside</td>\n", "      <td>Gtl</td>\n", "      <td>CollgCr</td>\n", "      <td>Norm</td>\n", "      <td>Norm</td>\n", "      <td>1Fam</td>\n", "      <td>2Story</td>\n", "      <td>7</td>\n", "      <td>5</td>\n", "      <td>2001</td>\n", "      <td>2002</td>\n", "      <td>Gable</td>\n", "      <td>CompShg</td>\n", "      <td>VinylSd</td>\n", "      <td>VinylSd</td>\n", "      <td>BrkFace</td>\n", "      <td>162.0</td>\n", "      <td>Gd</td>\n", "      <td>TA</td>\n", "      <td>PConc</td>\n", "      <td>Gd</td>\n", "      <td>TA</td>\n", "      <td>Mn</td>\n", "      <td>GLQ</td>\n", "      <td>486</td>\n", "      <td>Unf</td>\n", "      <td>0</td>\n", "      <td>434</td>\n", "      <td>920</td>\n", "      <td>GasA</td>\n", "      <td>Ex</td>\n", "      <td>Y</td>\n", "      <td>SBrkr</td>\n", "      <td>920</td>\n", "      <td>866</td>\n", "      <td>0</td>\n", "      <td>1786</td>\n", "      <td>1</td>\n", "      <td>0</td>\n", "      <td>2</td>\n", "      <td>1</td>\n", "      <td>3</td>\n", "      <td>1</td>\n", "      <td>Gd</td>\n", "      <td>6</td>\n", "      <td>Typ</td>\n", "      <td>1</td>\n", "      <td>TA</td>\n", "      <td>Attchd</td>\n", "      <td>2001.0</td>\n", "      <td>RFn</td>\n", "      <td>2</td>\n", "      <td>608</td>\n", "      <td>TA</td>\n", "      <td>TA</td>\n", "      <td>Y</td>\n", "      <td>0</td>\n", "      <td>42</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>0</td>\n", "      <td>9</td>\n", "      <td>2008</td>\n", "      <td>WD</td>\n", "      <td>Normal</td>\n", "      <td>223500</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>4</td>\n", "      <td>70</td>\n", "      <td>RL</td>\n", "      <td>60.0</td>\n", "      <td>9550</td>\n", "      <td>Pave</td>\n", "      <td>NaN</td>\n", "      <td>IR1</td>\n", "      <td>Lvl</td>\n", "      <td>AllPub</td>\n", "      <td>Corner</td>\n", "      <td>Gtl</td>\n", "      <td>Crawfor</td>\n", "      <td>Norm</td>\n", "      <td>Norm</td>\n", "      <td>1Fam</td>\n", "      <td>2Story</td>\n", "      <td>7</td>\n", "      <td>5</td>\n", "      <td>1915</td>\n", "      <td>1970</td>\n", "      <td>Gable</td>\n", "      <td>CompShg</td>\n", "      <td>Wd Sdng</td>\n", "      <td>Wd Shng</td>\n", "      <td>NaN</td>\n", "      <td>0.0</td>\n", "      <td>TA</td>\n", "      <td>TA</td>\n", "      <td>BrkTil</td>\n", "      <td>TA</td>\n", "      <td>Gd</td>\n", "      <td>No</td>\n", "      <td>ALQ</td>\n", "      <td>216</td>\n", "      <td>Unf</td>\n", "      <td>0</td>\n", "      <td>540</td>\n", "      <td>756</td>\n", "      <td>GasA</td>\n", "      <td>Gd</td>\n", "      <td>Y</td>\n", "      <td>SBrkr</td>\n", "      <td>961</td>\n", "      <td>756</td>\n", "      <td>0</td>\n", "      <td>1717</td>\n", "      <td>1</td>\n", "      <td>0</td>\n", "      <td>1</td>\n", "      <td>0</td>\n", "      <td>3</td>\n", "      <td>1</td>\n", "      <td>Gd</td>\n", "      <td>7</td>\n", "      <td>Typ</td>\n", "      <td>1</td>\n", "      <td>Gd</td>\n", "      <td>Detchd</td>\n", "      <td>1998.0</td>\n", "      <td>Unf</td>\n", "      <td>3</td>\n", "      <td>642</td>\n", "      <td>TA</td>\n", "      <td>TA</td>\n", "      <td>Y</td>\n", "      <td>0</td>\n", "      <td>35</td>\n", "      <td>272</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>0</td>\n", "      <td>2</td>\n", "      <td>2006</td>\n", "      <td>WD</td>\n", "      <td>Abnorml</td>\n", "      <td>140000</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>5</td>\n", "      <td>60</td>\n", "      <td>RL</td>\n", "      <td>84.0</td>\n", "      <td>14260</td>\n", "      <td>Pave</td>\n", "      <td>NaN</td>\n", "      <td>IR1</td>\n", "      <td>Lvl</td>\n", "      <td>AllPub</td>\n", "      <td>FR2</td>\n", "      <td>Gtl</td>\n", "      <td>NoRidge</td>\n", "      <td>Norm</td>\n", "      <td>Norm</td>\n", "      <td>1Fam</td>\n", "      <td>2Story</td>\n", "      <td>8</td>\n", "      <td>5</td>\n", "      <td>2000</td>\n", "      <td>2000</td>\n", "      <td>Gable</td>\n", "      <td>CompShg</td>\n", "      <td>VinylSd</td>\n", "      <td>VinylSd</td>\n", "      <td>BrkFace</td>\n", "      <td>350.0</td>\n", "      <td>Gd</td>\n", "      <td>TA</td>\n", "      <td>PConc</td>\n", "      <td>Gd</td>\n", "      <td>TA</td>\n", "      <td>Av</td>\n", "      <td>GLQ</td>\n", "      <td>655</td>\n", "      <td>Unf</td>\n", "      <td>0</td>\n", "      <td>490</td>\n", "      <td>1145</td>\n", "      <td>GasA</td>\n", "      <td>Ex</td>\n", "      <td>Y</td>\n", "      <td>SBrkr</td>\n", "      <td>1145</td>\n", "      <td>1053</td>\n", "      <td>0</td>\n", "      <td>2198</td>\n", "      <td>1</td>\n", "      <td>0</td>\n", "      <td>2</td>\n", "      <td>1</td>\n", "      <td>4</td>\n", "      <td>1</td>\n", "      <td>Gd</td>\n", "      <td>9</td>\n", "      <td>Typ</td>\n", "      <td>1</td>\n", "      <td>TA</td>\n", "      <td>Attchd</td>\n", "      <td>2000.0</td>\n", "      <td>RFn</td>\n", "      <td>3</td>\n", "      <td>836</td>\n", "      <td>TA</td>\n", "      <td>TA</td>\n", "      <td>Y</td>\n", "      <td>192</td>\n", "      <td>84</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>0</td>\n", "      <td>12</td>\n", "      <td>2008</td>\n", "      <td>WD</td>\n", "      <td>Normal</td>\n", "      <td>250000</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>\n", "    <div class=\"colab-df-buttons\">\n", "\n", "  <div class=\"colab-df-container\">\n", "    <button class=\"colab-df-convert\" onclick=\"convertToInteractive('df-2b8613a6-2f7f-4105-b2cc-461820f319bd')\"\n", "            title=\"Convert this dataframe to an interactive table.\"\n", "            style=\"display:none;\">\n", "\n", "  <svg xmlns=\"http://www.w3.org/2000/svg\" height=\"24px\" viewBox=\"0 -960 960 960\">\n", "    <path d=\"M120-120v-720h720v720H120Zm60-500h600v-160H180v160Zm220 220h160v-160H400v160Zm0 220h160v-160H400v160ZM180-400h160v-160H180v160Zm440 0h160v-160H620v160ZM180-180h160v-160H180v160Zm440 0h160v-160H620v160Z\"/>\n", "  </svg>\n", "    </button>\n", "\n", "  <style>\n", "    .colab-df-container {\n", "      display:flex;\n", "      gap: 12px;\n", "    }\n", "\n", "    .colab-df-convert {\n", "      background-color: #E8F0FE;\n", "      border: none;\n", "      border-radius: 50%;\n", "      cursor: pointer;\n", "      display: none;\n", "      fill: #1967D2;\n", "      height: 32px;\n", "      padding: 0 0 0 0;\n", "      width: 32px;\n", "    }\n", "\n", "    .colab-df-convert:hover {\n", "      background-color: #E2EBFA;\n", "      box-shadow: 0px 1px 2px rgba(60, 64, 67, 0.3), 0px 1px 3px 1px rgba(60, 64, 67, 0.15);\n", "      fill: #174EA6;\n", "    }\n", "\n", "    .colab-df-buttons div {\n", "      margin-bottom: 4px;\n", "    }\n", "\n", "    [theme=dark] .colab-df-convert {\n", "      background-color: #3B4455;\n", "      fill: #D2E3FC;\n", "    }\n", "\n", "    [theme=dark] .colab-df-convert:hover {\n", "      background-color: #434B5C;\n", "      box-shadow: 0px 1px 3px 1px rgba(0, 0, 0, 0.15);\n", "      filter: drop-shadow(0px 1px 2px rgba(0, 0, 0, 0.3));\n", "      fill: #FFFFFF;\n", "    }\n", "  </style>\n", "\n", "    <script>\n", "      const buttonEl =\n", "        document.querySelector('#df-2b8613a6-2f7f-4105-b2cc-461820f319bd button.colab-df-convert');\n", "      buttonEl.style.display =\n", "        google.colab.kernel.accessAllowed ? 'block' : 'none';\n", "\n", "      async function convertToInteractive(key) {\n", "        const element = document.querySelector('#df-2b8613a6-2f7f-4105-b2cc-461820f319bd');\n", "        const dataTable =\n", "          await google.colab.kernel.invokeFunction('convertToInteractive',\n", "                                                    [key], {});\n", "        if (!dataTable) return;\n", "\n", "        const docLinkHtml = 'Like what you see? Visit the ' +\n", "          '<a target=\"_blank\" href=https://colab.research.google.com/notebooks/data_table.ipynb>data table notebook</a>'\n", "          + ' to learn more about interactive tables.';\n", "        element.innerHTML = '';\n", "        dataTable['output_type'] = 'display_data';\n", "        await google.colab.output.renderOutput(dataTable, element);\n", "        const docLink = document.createElement('div');\n", "        docLink.innerHTML = docLinkHtml;\n", "        element.appendChild(docLink);\n", "      }\n", "    </script>\n", "  </div>\n", "\n", "\n", "    <div id=\"df-68d24604-0ed2-4287-8c05-b2afb3e69db5\">\n", "      <button class=\"colab-df-quickchart\" onclick=\"quickchart('df-68d24604-0ed2-4287-8c05-b2afb3e69db5')\"\n", "                title=\"Suggest charts\"\n", "                style=\"display:none;\">\n", "\n", "<svg xmlns=\"http://www.w3.org/2000/svg\" height=\"24px\"viewBox=\"0 0 24 24\"\n", "     width=\"24px\">\n", "    <g>\n", "        <path d=\"M19 3H5c-1.1 0-2 .9-2 2v14c0 1.1.9 2 2 2h14c1.1 0 2-.9 2-2V5c0-1.1-.9-2-2-2zM9 17H7v-7h2v7zm4 0h-2V7h2v10zm4 0h-2v-4h2v4z\"/>\n", "    </g>\n", "</svg>\n", "      </button>\n", "\n", "<style>\n", "  .colab-df-quickchart {\n", "      --bg-color: #E8F0FE;\n", "      --fill-color: #1967D2;\n", "      --hover-bg-color: #E2EBFA;\n", "      --hover-fill-color: #174EA6;\n", "      --disabled-fill-color: #AAA;\n", "      --disabled-bg-color: #DDD;\n", "  }\n", "\n", "  [theme=dark] .colab-df-quickchart {\n", "      --bg-color: #3B4455;\n", "      --fill-color: #D2E3FC;\n", "      --hover-bg-color: #434B5C;\n", "      --hover-fill-color: #FFFFFF;\n", "      --disabled-bg-color: #3B4455;\n", "      --disabled-fill-color: #666;\n", "  }\n", "\n", "  .colab-df-quickchart {\n", "    background-color: var(--bg-color);\n", "    border: none;\n", "    border-radius: 50%;\n", "    cursor: pointer;\n", "    display: none;\n", "    fill: var(--fill-color);\n", "    height: 32px;\n", "    padding: 0;\n", "    width: 32px;\n", "  }\n", "\n", "  .colab-df-quickchart:hover {\n", "    background-color: var(--hover-bg-color);\n", "    box-shadow: 0 1px 2px rgba(60, 64, 67, 0.3), 0 1px 3px 1px rgba(60, 64, 67, 0.15);\n", "    fill: var(--button-hover-fill-color);\n", "  }\n", "\n", "  .colab-df-quickchart-complete:disabled,\n", "  .colab-df-quickchart-complete:disabled:hover {\n", "    background-color: var(--disabled-bg-color);\n", "    fill: var(--disabled-fill-color);\n", "    box-shadow: none;\n", "  }\n", "\n", "  .colab-df-spinner {\n", "    border: 2px solid var(--fill-color);\n", "    border-color: transparent;\n", "    border-bottom-color: var(--fill-color);\n", "    animation:\n", "      spin 1s steps(1) infinite;\n", "  }\n", "\n", "  @keyframes spin {\n", "    0% {\n", "      border-color: transparent;\n", "      border-bottom-color: var(--fill-color);\n", "      border-left-color: var(--fill-color);\n", "    }\n", "    20% {\n", "      border-color: transparent;\n", "      border-left-color: var(--fill-color);\n", "      border-top-color: var(--fill-color);\n", "    }\n", "    30% {\n", "      border-color: transparent;\n", "      border-left-color: var(--fill-color);\n", "      border-top-color: var(--fill-color);\n", "      border-right-color: var(--fill-color);\n", "    }\n", "    40% {\n", "      border-color: transparent;\n", "      border-right-color: var(--fill-color);\n", "      border-top-color: var(--fill-color);\n", "    }\n", "    60% {\n", "      border-color: transparent;\n", "      border-right-color: var(--fill-color);\n", "    }\n", "    80% {\n", "      border-color: transparent;\n", "      border-right-color: var(--fill-color);\n", "      border-bottom-color: var(--fill-color);\n", "    }\n", "    90% {\n", "      border-color: transparent;\n", "      border-bottom-color: var(--fill-color);\n", "    }\n", "  }\n", "</style>\n", "\n", "      <script>\n", "        async function quickchart(key) {\n", "          const quickchartButtonEl =\n", "            document.querySelector('#' + key + ' button');\n", "          quickchartButtonEl.disabled = true;  // To prevent multiple clicks.\n", "          quickchartButtonEl.classList.add('colab-df-spinner');\n", "          try {\n", "            const charts = await google.colab.kernel.invokeFunction(\n", "                'suggest<PERSON><PERSON>s', [key], {});\n", "          } catch (error) {\n", "            console.error('Error during call to suggest<PERSON>harts:', error);\n", "          }\n", "          quickchartButtonEl.classList.remove('colab-df-spinner');\n", "          quickchartButtonEl.classList.add('colab-df-quickchart-complete');\n", "        }\n", "        (() => {\n", "          let quickchartButtonEl =\n", "            document.querySelector('#df-68d24604-0ed2-4287-8c05-b2afb3e69db5 button');\n", "          quickchartButtonEl.style.display =\n", "            google.colab.kernel.accessAllowed ? 'block' : 'none';\n", "        })();\n", "      </script>\n", "    </div>\n", "\n", "    </div>\n", "  </div>\n"], "application/vnd.google.colaboratory.intrinsic+json": {"type": "dataframe"}}, "metadata": {}}]}, {"cell_type": "code", "source": ["# Shape of raw data\n", "print(f\"Dataset loaded successfully. Shape: {df.shape}\")"], "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "2KqRR_8Pp-1a", "outputId": "b197a049-233a-401c-a2ab-b3c4e7d1151c"}, "execution_count": 73, "outputs": [{"output_type": "stream", "name": "stdout", "text": ["Dataset loaded successfully. Shape: (1460, 81)\n"]}]}, {"cell_type": "markdown", "source": [" # Check for Duplicates"], "metadata": {"id": "pFCsG12ps4Ku"}}, {"cell_type": "code", "source": ["# --- Check for Duplicates ---\n", "print(\"\\n--- Checking for Duplicate Rows ---\")\n", "df.duplicated().sum()\n"], "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "G9JLAEQbp-7G", "outputId": "ccc2f973-5bb1-456a-ea7f-0d60a0c53878"}, "execution_count": 74, "outputs": [{"output_type": "stream", "name": "stdout", "text": ["\n", "--- Checking for Duplicate Rows ---\n"]}, {"output_type": "execute_result", "data": {"text/plain": ["np.int64(0)"]}, "metadata": {}, "execution_count": 74}]}, {"cell_type": "markdown", "source": ["# Check for missing values"], "metadata": {"id": "N5LwSVhJtG19"}}, {"cell_type": "code", "source": ["missing_values = df.isnull().sum()\n", "missing_values = missing_values[missing_values > 0].sort_values(ascending=False)\n", "print(\"\\nFeatures with missing values:\")\n", "print(missing_values)"], "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "76m5AP61p-_4", "outputId": "dfbc8a56-2b03-48bd-9bf2-ff088eade6de"}, "execution_count": 75, "outputs": [{"output_type": "stream", "name": "stdout", "text": ["\n", "Features with missing values:\n", "PoolQC          1453\n", "MiscFeature     1406\n", "Alley           1369\n", "Fence           1179\n", "MasVnrType       872\n", "FireplaceQu      690\n", "LotFrontage      259\n", "GarageType        81\n", "GarageYrBlt       81\n", "Garage<PERSON><PERSON>sh      81\n", "GarageQual        81\n", "GarageCond        81\n", "BsmtExposure      38\n", "BsmtFinType2      38\n", "BsmtQual          37\n", "BsmtCond          37\n", "BsmtFinType1      37\n", "MasVnrArea         8\n", "Electrical         1\n", "dtype: int64\n"]}]}, {"cell_type": "markdown", "source": ["# Data Preprocessing & Feature Engineering"], "metadata": {"id": "KU4CTObXuPBZ"}}, {"cell_type": "code", "source": ["# Make copy to avoid modifying the original raw dataframe\n", "# --- Column Name Cleaning ---\n", "print(\"\\n--- Cleaning Column Names ---\")\n", "df.columns.tolist()"], "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "qPhm1iS0p_Ee", "outputId": "071f5a61-19b1-43b5-bd83-67312b6331db"}, "execution_count": 76, "outputs": [{"output_type": "stream", "name": "stdout", "text": ["\n", "--- Cleaning Column Names ---\n"]}, {"output_type": "execute_result", "data": {"text/plain": ["['Id',\n", " 'MSSubClass',\n", " 'MSZoning',\n", " 'LotFrontage',\n", " 'LotArea',\n", " 'Street',\n", " 'Alley',\n", " 'LotShape',\n", " 'LandContour',\n", " 'Utilities',\n", " 'LotConfig',\n", " 'LandSlope',\n", " 'Neighborhood',\n", " 'Condition1',\n", " 'Condition2',\n", " 'BldgType',\n", " 'HouseStyle',\n", " 'OverallQual',\n", " 'OverallCond',\n", " 'YearBuilt',\n", " 'YearRemodAdd',\n", " 'RoofStyle',\n", " 'RoofMatl',\n", " 'Exterior1st',\n", " 'Exterior2nd',\n", " 'MasVnrType',\n", " 'MasVnrArea',\n", " 'ExterQual',\n", " 'ExterCond',\n", " 'Foundation',\n", " 'BsmtQual',\n", " 'BsmtCond',\n", " 'BsmtExposure',\n", " 'BsmtFinType1',\n", " 'BsmtFinSF1',\n", " 'BsmtFinType2',\n", " 'BsmtFinSF2',\n", " 'BsmtUnfSF',\n", " 'TotalBsmtSF',\n", " 'Heating',\n", " 'HeatingQC',\n", " 'CentralAir',\n", " 'Electrical',\n", " '1stFlrSF',\n", " '2ndFlrSF',\n", " 'LowQualFinSF',\n", " 'GrLivArea',\n", " 'BsmtFullBath',\n", " 'BsmtHalfBath',\n", " 'FullBath',\n", " 'HalfBath',\n", " 'BedroomAbvGr',\n", " 'KitchenAbvGr',\n", " 'KitchenQual',\n", " 'TotRmsAbvGrd',\n", " 'Functional',\n", " 'Fireplaces',\n", " 'FireplaceQu',\n", " 'GarageType',\n", " 'GarageYrBlt',\n", " 'GarageF<PERSON>sh',\n", " 'GarageCars',\n", " 'GarageArea',\n", " 'GarageQual',\n", " 'GarageCond',\n", " 'PavedDrive',\n", " 'WoodDeckSF',\n", " 'OpenPorchSF',\n", " 'EnclosedPorch',\n", " '3SsnPorch',\n", " 'ScreenPorch',\n", " 'PoolArea',\n", " 'PoolQC',\n", " '<PERSON><PERSON>',\n", " 'MiscFeature',\n", " 'MiscVal',\n", " 'MoSold',\n", " 'Yr<PERSON><PERSON>',\n", " 'SaleType',\n", " 'SaleCondition',\n", " 'SalePrice']"]}, "metadata": {}, "execution_count": 76}]}, {"cell_type": "code", "source": ["df.columns = df.columns.str.lower().str.replace(\"[^\\w]\\s*\", \"\", regex=True)\n", "new_cols = df.columns.tolist()\n", "new_cols"], "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "XiRhes61p_Mt", "outputId": "ef29df23-1998-46ae-88f9-175563076765"}, "execution_count": 77, "outputs": [{"output_type": "execute_result", "data": {"text/plain": ["['id',\n", " 'mssubclass',\n", " 'mszoning',\n", " 'lotfrontage',\n", " 'lotarea',\n", " 'street',\n", " 'alley',\n", " 'lotshape',\n", " 'landcontour',\n", " 'utilities',\n", " 'lotconfig',\n", " 'landslope',\n", " 'neighborhood',\n", " 'condition1',\n", " 'condition2',\n", " 'bldgtype',\n", " 'housestyle',\n", " 'overallqual',\n", " 'overallcond',\n", " 'yearbuilt',\n", " 'yearremodadd',\n", " 'roofstyle',\n", " 'roofmatl',\n", " 'exterior1st',\n", " 'exterior2nd',\n", " 'masvnrtype',\n", " 'masvnrarea',\n", " 'exterqual',\n", " 'extercond',\n", " 'foundation',\n", " 'bsmtqual',\n", " 'bsmtcond',\n", " 'bsmtexposure',\n", " 'bsmtfintype1',\n", " 'bsmtfinsf1',\n", " 'bsmtfintype2',\n", " 'bsmtfinsf2',\n", " 'bsmtunfsf',\n", " 'totalbsmtsf',\n", " 'heating',\n", " 'heatingqc',\n", " 'centralair',\n", " 'electrical',\n", " '1stflrsf',\n", " '2ndflrsf',\n", " 'lowqualfinsf',\n", " 'grlivarea',\n", " 'bsmtfullbath',\n", " 'bsmthalfbath',\n", " 'fullbath',\n", " 'halfbath',\n", " 'bedroomabvgr',\n", " 'kitchenabvgr',\n", " 'kitchenqual',\n", " 'totrmsabvgrd',\n", " 'functional',\n", " 'fireplaces',\n", " 'fireplacequ',\n", " 'garagetype',\n", " 'garageyrblt',\n", " 'garagefinish',\n", " 'garagecars',\n", " 'garagearea',\n", " 'garagequal',\n", " 'garagecond',\n", " 'paveddrive',\n", " 'wooddecksf',\n", " 'openporchsf',\n", " 'enclosedporch',\n", " '3ssnporch',\n", " 'screenporch',\n", " 'poolarea',\n", " 'poolqc',\n", " 'fence',\n", " 'miscfeature',\n", " 'miscval',\n", " 'mosold',\n", " 'yrsold',\n", " 'saletype',\n", " 'salecondition',\n", " 'saleprice']"]}, "metadata": {}, "execution_count": 77}]}, {"cell_type": "code", "source": ["# Create a mapping for reference if needed, handling potential dulicates cautiously\n", "col_mapping = {old: new for old, new in zip(df.columns, new_cols)}\n", "print(\"Column names cleaned (lowercase, no spaces/special chars).\")"], "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "cIlvWGGmp_Rh", "outputId": "062ababa-5b07-418d-ebfe-4add07eed1a3"}, "execution_count": 78, "outputs": [{"output_type": "stream", "name": "stdout", "text": ["Column names cleaned (lowercase, no spaces/special chars).\n"]}]}, {"cell_type": "code", "source": ["df_new = df.copy()"], "metadata": {"id": "xgAxSBUqp_Ws"}, "execution_count": 79, "outputs": []}, {"cell_type": "code", "source": ["df_new"], "metadata": {"colab": {"base_uri": "https://localhost:8080/", "height": 443}, "id": "OZByzpPFp_b1", "outputId": "83e8a803-5520-413e-ffe2-f78fbb9b1e6e"}, "execution_count": 80, "outputs": [{"output_type": "execute_result", "data": {"text/plain": ["        id  mssubclass mszoning  lotfrontage  lotarea street alley lotshape  \\\n", "0        1          60       RL         65.0     8450   Pave   NaN      Reg   \n", "1        2          20       RL         80.0     9600   Pave   NaN      Reg   \n", "2        3          60       RL         68.0    11250   Pave   NaN      IR1   \n", "3        4          70       RL         60.0     9550   Pave   NaN      IR1   \n", "4        5          60       RL         84.0    14260   Pave   NaN      IR1   \n", "...    ...         ...      ...          ...      ...    ...   ...      ...   \n", "1455  1456          60       RL         62.0     7917   Pave   NaN      Reg   \n", "1456  1457          20       RL         85.0    13175   Pave   NaN      Reg   \n", "1457  1458          70       RL         66.0     9042   Pave   NaN      Reg   \n", "1458  1459          20       RL         68.0     9717   Pave   NaN      Reg   \n", "1459  1460          20       RL         75.0     9937   Pave   NaN      Reg   \n", "\n", "     landcontour utilities lotconfig landslope neighborhood condition1  \\\n", "0            Lvl    AllPub    Inside       Gtl      CollgCr       Norm   \n", "1            Lvl    AllPub       FR2       Gtl      Veenker      Feedr   \n", "2            Lvl    AllPub    Inside       Gtl      CollgCr       Norm   \n", "3            Lvl    AllPub    Corner       Gtl      Crawfor       Norm   \n", "4            Lvl    AllPub       FR2       Gtl      NoRidge       Norm   \n", "...          ...       ...       ...       ...          ...        ...   \n", "1455         Lvl    AllPub    Inside       Gtl      Gilbert       <PERSON>   \n", "1456         Lvl    AllPub    Inside       Gtl       NWAmes       Norm   \n", "1457         Lvl    AllPub    Inside       Gtl      Crawfor       Norm   \n", "1458         Lvl    AllPub    Inside       Gtl        NAmes       Norm   \n", "1459         Lvl    AllPub    Inside       Gtl      Edwards       Norm   \n", "\n", "     condition2 bldgtype housestyle  overallqual  overallcond  yearbuilt  \\\n", "0          Norm     1Fam     2Story            7            5       2003   \n", "1          Norm     1Fam     1Story            6            8       1976   \n", "2          Norm     1Fam     2Story            7            5       2001   \n", "3          Norm     1Fam     2Story            7            5       1915   \n", "4          Norm     1Fam     2Story            8            5       2000   \n", "...         ...      ...        ...          ...          ...        ...   \n", "1455       Norm     1Fam     2Story            6            5       1999   \n", "1456       Norm     1Fam     1Story            6            6       1978   \n", "1457       Norm     1Fam     2Story            7            9       1941   \n", "1458       Norm     1Fam     1Story            5            6       1950   \n", "1459       Norm     1Fam     1Story            5            6       1965   \n", "\n", "      yearremodadd roofstyle roofmatl exterior1st exterior2nd masvnrtype  \\\n", "0             2003     Gable  CompShg     VinylSd     VinylSd    BrkFace   \n", "1             1976     Gable  CompShg     MetalSd     MetalSd        NaN   \n", "2             2002     Gable  CompShg     VinylSd     VinylSd    BrkFace   \n", "3             1970     Gable  CompShg     Wd Sdng     Wd Shng        NaN   \n", "4             2000     Gable  CompShg     VinylSd     VinylSd    BrkFace   \n", "...            ...       ...      ...         ...         ...        ...   \n", "1455          2000     Gable  CompShg     VinylSd     VinylSd        NaN   \n", "1456          1988     Gable  CompShg     Plywood     Plywood      Stone   \n", "1457          2006     Gable  CompShg     CemntBd     CmentBd        NaN   \n", "1458          1996       Hip  CompShg     MetalSd     MetalSd        NaN   \n", "1459          1965     Gable  CompShg     HdBoard     HdBoard        NaN   \n", "\n", "      masvnrarea exterqual extercond foundation bsmtqual bsmtcond  \\\n", "0          196.0        Gd        TA      PConc       Gd       TA   \n", "1            0.0        TA        TA     CBlock       Gd       TA   \n", "2          162.0        Gd        TA      PConc       Gd       TA   \n", "3            0.0        TA        TA     BrkTil       TA       Gd   \n", "4          350.0        Gd        TA      PConc       Gd       TA   \n", "...          ...       ...       ...        ...      ...      ...   \n", "1455         0.0        TA        TA      PConc       Gd       TA   \n", "1456       119.0        TA        TA     CBlock       Gd       TA   \n", "1457         0.0        Ex        Gd      Stone       TA       Gd   \n", "1458         0.0        TA        TA     CBlock       TA       TA   \n", "1459         0.0        Gd        TA     CBlock       TA       TA   \n", "\n", "     bsmtexposure bsmtfintype1  bsmtfinsf1 bsmtfintype2  bsmtfinsf2  \\\n", "0              No          GLQ         706          Unf           0   \n", "1              Gd          ALQ         978          Unf           0   \n", "2              Mn          GLQ         486          Unf           0   \n", "3              No          ALQ         216          Unf           0   \n", "4              Av          GLQ         655          Unf           0   \n", "...           ...          ...         ...          ...         ...   \n", "1455           No          Unf           0          Unf           0   \n", "1456           No          ALQ         790          Rec         163   \n", "1457           No          GLQ         275          Unf           0   \n", "1458           Mn          GLQ          49          Rec        1029   \n", "1459           No          BLQ         830          LwQ         290   \n", "\n", "      bsmtunfsf  totalbsmtsf heating heatingqc centralair electrical  \\\n", "0           150          856    GasA        Ex          Y      SBrkr   \n", "1           284         1262    GasA        Ex          Y      SBrkr   \n", "2           434          920    GasA        Ex          Y      SBrkr   \n", "3           540          756    GasA        Gd          Y      SBrkr   \n", "4           490         1145    GasA        Ex          Y      SBrkr   \n", "...         ...          ...     ...       ...        ...        ...   \n", "1455        953          953    GasA        Ex          Y      SBrkr   \n", "1456        589         1542    GasA        TA          Y      SBrkr   \n", "1457        877         1152    GasA        Ex          Y      SBrkr   \n", "1458          0         1078    GasA        Gd          Y      FuseA   \n", "1459        136         1256    GasA        Gd          Y      SBrkr   \n", "\n", "      1stflrsf  2ndflrsf  lowqualfinsf  grlivarea  bsmtfullbath  bsmthalfbath  \\\n", "0          856       854             0       1710             1             0   \n", "1         1262         0             0       1262             0             1   \n", "2          920       866             0       1786             1             0   \n", "3          961       756             0       1717             1             0   \n", "4         1145      1053             0       2198             1             0   \n", "...        ...       ...           ...        ...           ...           ...   \n", "1455       953       694             0       1647             0             0   \n", "1456      2073         0             0       2073             1             0   \n", "1457      1188      1152             0       2340             0             0   \n", "1458      1078         0             0       1078             1             0   \n", "1459      1256         0             0       1256             1             0   \n", "\n", "      fullbath  halfbath  bedroomabvgr  kitchenabvgr kitchenqual  \\\n", "0            2         1             3             1          Gd   \n", "1            2         0             3             1          TA   \n", "2            2         1             3             1          Gd   \n", "3            1         0             3             1          Gd   \n", "4            2         1             4             1          Gd   \n", "...        ...       ...           ...           ...         ...   \n", "1455         2         1             3             1          TA   \n", "1456         2         0             3             1          TA   \n", "1457         2         0             4             1          Gd   \n", "1458         1         0             2             1          Gd   \n", "1459         1         1             3             1          TA   \n", "\n", "      totrmsabvgrd functional  fireplaces fireplacequ garagetype  garageyrblt  \\\n", "0                8        Typ           0         NaN     Attchd       2003.0   \n", "1                6        Typ           1          TA     Attchd       1976.0   \n", "2                6        Typ           1          TA     Attchd       2001.0   \n", "3                7        Typ           1          Gd     Detchd       1998.0   \n", "4                9        Typ           1          TA     Attchd       2000.0   \n", "...            ...        ...         ...         ...        ...          ...   \n", "1455             7        Typ           1          TA     Attchd       1999.0   \n", "1456             7       Min1           2          TA     Attchd       1978.0   \n", "1457             9        Typ           2          Gd     Attchd       1941.0   \n", "1458             5        Typ           0         NaN     Attchd       1950.0   \n", "1459             6        Typ           0         NaN     Attchd       1965.0   \n", "\n", "     garagefinish  garagecars  garagearea garagequal garagecond paveddrive  \\\n", "0             RFn           2         548         TA         TA          Y   \n", "1             RFn           2         460         TA         TA          Y   \n", "2             RFn           2         608         TA         TA          Y   \n", "3             Unf           3         642         TA         TA          Y   \n", "4             RFn           3         836         TA         TA          Y   \n", "...           ...         ...         ...        ...        ...        ...   \n", "1455          RFn           2         460         TA         TA          Y   \n", "1456          Unf           2         500         TA         TA          Y   \n", "1457          RFn           1         252         TA         TA          Y   \n", "1458          Unf           1         240         TA         TA          Y   \n", "1459          Fin           1         276         TA         TA          Y   \n", "\n", "      wooddecksf  openporchsf  enclosedporch  3ssnporch  screenporch  \\\n", "0              0           61              0          0            0   \n", "1            298            0              0          0            0   \n", "2              0           42              0          0            0   \n", "3              0           35            272          0            0   \n", "4            192           84              0          0            0   \n", "...          ...          ...            ...        ...          ...   \n", "1455           0           40              0          0            0   \n", "1456         349            0              0          0            0   \n", "1457           0           60              0          0            0   \n", "1458         366            0            112          0            0   \n", "1459         736           68              0          0            0   \n", "\n", "      poolarea poolqc  fence miscfeature  miscval  mosold  yrsold saletype  \\\n", "0            0    NaN    NaN         NaN        0       2    2008       WD   \n", "1            0    NaN    NaN         NaN        0       5    2007       WD   \n", "2            0    NaN    NaN         NaN        0       9    2008       WD   \n", "3            0    NaN    NaN         NaN        0       2    2006       WD   \n", "4            0    NaN    NaN         NaN        0      12    2008       WD   \n", "...        ...    ...    ...         ...      ...     ...     ...      ...   \n", "1455         0    NaN    NaN         NaN        0       8    2007       WD   \n", "1456         0    NaN  MnPrv         NaN        0       2    2010       WD   \n", "1457         0    NaN  GdPrv        Shed     2500       5    2010       WD   \n", "1458         0    NaN    NaN         NaN        0       4    2010       WD   \n", "1459         0    NaN    NaN         NaN        0       6    2008       WD   \n", "\n", "     salecondition  saleprice  \n", "0           Normal     208500  \n", "1           Normal     181500  \n", "2           Normal     223500  \n", "3          Abnorml     140000  \n", "4           Normal     250000  \n", "...            ...        ...  \n", "1455        Normal     175000  \n", "1456        Normal     210000  \n", "1457        Normal     266500  \n", "1458        Normal     142125  \n", "1459        Normal     147500  \n", "\n", "[1460 rows x 81 columns]"], "text/html": ["\n", "  <div id=\"df-efa46eda-c344-45a8-af30-0b15c51ed25f\" class=\"colab-df-container\">\n", "    <div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>id</th>\n", "      <th>mssubclass</th>\n", "      <th>mszoning</th>\n", "      <th>lotfrontage</th>\n", "      <th>lotarea</th>\n", "      <th>street</th>\n", "      <th>alley</th>\n", "      <th>lotshape</th>\n", "      <th>landcontour</th>\n", "      <th>utilities</th>\n", "      <th>lotconfig</th>\n", "      <th>landslope</th>\n", "      <th>neighborhood</th>\n", "      <th>condition1</th>\n", "      <th>condition2</th>\n", "      <th>bldgtype</th>\n", "      <th>housestyle</th>\n", "      <th>overallqual</th>\n", "      <th>overallcond</th>\n", "      <th>yearbuilt</th>\n", "      <th>yearremodadd</th>\n", "      <th>roofstyle</th>\n", "      <th>roofmatl</th>\n", "      <th>exterior1st</th>\n", "      <th>exterior2nd</th>\n", "      <th>masvnrtype</th>\n", "      <th>masvnrarea</th>\n", "      <th>exterqual</th>\n", "      <th>extercond</th>\n", "      <th>foundation</th>\n", "      <th>bsmtqual</th>\n", "      <th>bsmtcond</th>\n", "      <th>bsmtexposure</th>\n", "      <th>bsmtfintype1</th>\n", "      <th>bsmtfinsf1</th>\n", "      <th>bsmtfintype2</th>\n", "      <th>bsmtfinsf2</th>\n", "      <th>bsmtunfsf</th>\n", "      <th>totalbsmtsf</th>\n", "      <th>heating</th>\n", "      <th>heatingqc</th>\n", "      <th>centralair</th>\n", "      <th>electrical</th>\n", "      <th>1stflrsf</th>\n", "      <th>2ndflrsf</th>\n", "      <th>lowqualfinsf</th>\n", "      <th>grlivarea</th>\n", "      <th>bsm<PERSON><PERSON><PERSON><PERSON>h</th>\n", "      <th>bsm<PERSON><PERSON>bath</th>\n", "      <th>fullbath</th>\n", "      <th>halfbath</th>\n", "      <th>bedroomabvgr</th>\n", "      <th>kitchenabvgr</th>\n", "      <th>kitchenqual</th>\n", "      <th>totrmsabvgrd</th>\n", "      <th>functional</th>\n", "      <th>fireplaces</th>\n", "      <th>fireplacequ</th>\n", "      <th>garagetype</th>\n", "      <th>garageyrblt</th>\n", "      <th>garagefinish</th>\n", "      <th>garagecars</th>\n", "      <th>garagearea</th>\n", "      <th>garagequal</th>\n", "      <th>garagecond</th>\n", "      <th>paveddrive</th>\n", "      <th>wooddecksf</th>\n", "      <th>openporchsf</th>\n", "      <th>enclosedporch</th>\n", "      <th>3ssnporch</th>\n", "      <th>screenporch</th>\n", "      <th>poolarea</th>\n", "      <th>poolqc</th>\n", "      <th>fence</th>\n", "      <th>miscfeature</th>\n", "      <th>miscval</th>\n", "      <th>mosold</th>\n", "      <th>yrsold</th>\n", "      <th>saletype</th>\n", "      <th>salecondition</th>\n", "      <th>saleprice</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>1</td>\n", "      <td>60</td>\n", "      <td>RL</td>\n", "      <td>65.0</td>\n", "      <td>8450</td>\n", "      <td>Pave</td>\n", "      <td>NaN</td>\n", "      <td>Reg</td>\n", "      <td>Lvl</td>\n", "      <td>AllPub</td>\n", "      <td>Inside</td>\n", "      <td>Gtl</td>\n", "      <td>CollgCr</td>\n", "      <td>Norm</td>\n", "      <td>Norm</td>\n", "      <td>1Fam</td>\n", "      <td>2Story</td>\n", "      <td>7</td>\n", "      <td>5</td>\n", "      <td>2003</td>\n", "      <td>2003</td>\n", "      <td>Gable</td>\n", "      <td>CompShg</td>\n", "      <td>VinylSd</td>\n", "      <td>VinylSd</td>\n", "      <td>BrkFace</td>\n", "      <td>196.0</td>\n", "      <td>Gd</td>\n", "      <td>TA</td>\n", "      <td>PConc</td>\n", "      <td>Gd</td>\n", "      <td>TA</td>\n", "      <td>No</td>\n", "      <td>GLQ</td>\n", "      <td>706</td>\n", "      <td>Unf</td>\n", "      <td>0</td>\n", "      <td>150</td>\n", "      <td>856</td>\n", "      <td>GasA</td>\n", "      <td>Ex</td>\n", "      <td>Y</td>\n", "      <td>SBrkr</td>\n", "      <td>856</td>\n", "      <td>854</td>\n", "      <td>0</td>\n", "      <td>1710</td>\n", "      <td>1</td>\n", "      <td>0</td>\n", "      <td>2</td>\n", "      <td>1</td>\n", "      <td>3</td>\n", "      <td>1</td>\n", "      <td>Gd</td>\n", "      <td>8</td>\n", "      <td>Typ</td>\n", "      <td>0</td>\n", "      <td>NaN</td>\n", "      <td>Attchd</td>\n", "      <td>2003.0</td>\n", "      <td>RFn</td>\n", "      <td>2</td>\n", "      <td>548</td>\n", "      <td>TA</td>\n", "      <td>TA</td>\n", "      <td>Y</td>\n", "      <td>0</td>\n", "      <td>61</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>0</td>\n", "      <td>2</td>\n", "      <td>2008</td>\n", "      <td>WD</td>\n", "      <td>Normal</td>\n", "      <td>208500</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>2</td>\n", "      <td>20</td>\n", "      <td>RL</td>\n", "      <td>80.0</td>\n", "      <td>9600</td>\n", "      <td>Pave</td>\n", "      <td>NaN</td>\n", "      <td>Reg</td>\n", "      <td>Lvl</td>\n", "      <td>AllPub</td>\n", "      <td>FR2</td>\n", "      <td>Gtl</td>\n", "      <td><PERSON><PERSON><PERSON></td>\n", "      <td><PERSON>edr</td>\n", "      <td>Norm</td>\n", "      <td>1Fam</td>\n", "      <td>1Story</td>\n", "      <td>6</td>\n", "      <td>8</td>\n", "      <td>1976</td>\n", "      <td>1976</td>\n", "      <td>Gable</td>\n", "      <td>CompShg</td>\n", "      <td>MetalSd</td>\n", "      <td>MetalSd</td>\n", "      <td>NaN</td>\n", "      <td>0.0</td>\n", "      <td>TA</td>\n", "      <td>TA</td>\n", "      <td>CBlock</td>\n", "      <td>Gd</td>\n", "      <td>TA</td>\n", "      <td>Gd</td>\n", "      <td>ALQ</td>\n", "      <td>978</td>\n", "      <td>Unf</td>\n", "      <td>0</td>\n", "      <td>284</td>\n", "      <td>1262</td>\n", "      <td>GasA</td>\n", "      <td>Ex</td>\n", "      <td>Y</td>\n", "      <td>SBrkr</td>\n", "      <td>1262</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>1262</td>\n", "      <td>0</td>\n", "      <td>1</td>\n", "      <td>2</td>\n", "      <td>0</td>\n", "      <td>3</td>\n", "      <td>1</td>\n", "      <td>TA</td>\n", "      <td>6</td>\n", "      <td>Typ</td>\n", "      <td>1</td>\n", "      <td>TA</td>\n", "      <td>Attchd</td>\n", "      <td>1976.0</td>\n", "      <td>RFn</td>\n", "      <td>2</td>\n", "      <td>460</td>\n", "      <td>TA</td>\n", "      <td>TA</td>\n", "      <td>Y</td>\n", "      <td>298</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>0</td>\n", "      <td>5</td>\n", "      <td>2007</td>\n", "      <td>WD</td>\n", "      <td>Normal</td>\n", "      <td>181500</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>3</td>\n", "      <td>60</td>\n", "      <td>RL</td>\n", "      <td>68.0</td>\n", "      <td>11250</td>\n", "      <td>Pave</td>\n", "      <td>NaN</td>\n", "      <td>IR1</td>\n", "      <td>Lvl</td>\n", "      <td>AllPub</td>\n", "      <td>Inside</td>\n", "      <td>Gtl</td>\n", "      <td>CollgCr</td>\n", "      <td>Norm</td>\n", "      <td>Norm</td>\n", "      <td>1Fam</td>\n", "      <td>2Story</td>\n", "      <td>7</td>\n", "      <td>5</td>\n", "      <td>2001</td>\n", "      <td>2002</td>\n", "      <td>Gable</td>\n", "      <td>CompShg</td>\n", "      <td>VinylSd</td>\n", "      <td>VinylSd</td>\n", "      <td>BrkFace</td>\n", "      <td>162.0</td>\n", "      <td>Gd</td>\n", "      <td>TA</td>\n", "      <td>PConc</td>\n", "      <td>Gd</td>\n", "      <td>TA</td>\n", "      <td>Mn</td>\n", "      <td>GLQ</td>\n", "      <td>486</td>\n", "      <td>Unf</td>\n", "      <td>0</td>\n", "      <td>434</td>\n", "      <td>920</td>\n", "      <td>GasA</td>\n", "      <td>Ex</td>\n", "      <td>Y</td>\n", "      <td>SBrkr</td>\n", "      <td>920</td>\n", "      <td>866</td>\n", "      <td>0</td>\n", "      <td>1786</td>\n", "      <td>1</td>\n", "      <td>0</td>\n", "      <td>2</td>\n", "      <td>1</td>\n", "      <td>3</td>\n", "      <td>1</td>\n", "      <td>Gd</td>\n", "      <td>6</td>\n", "      <td>Typ</td>\n", "      <td>1</td>\n", "      <td>TA</td>\n", "      <td>Attchd</td>\n", "      <td>2001.0</td>\n", "      <td>RFn</td>\n", "      <td>2</td>\n", "      <td>608</td>\n", "      <td>TA</td>\n", "      <td>TA</td>\n", "      <td>Y</td>\n", "      <td>0</td>\n", "      <td>42</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>0</td>\n", "      <td>9</td>\n", "      <td>2008</td>\n", "      <td>WD</td>\n", "      <td>Normal</td>\n", "      <td>223500</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>4</td>\n", "      <td>70</td>\n", "      <td>RL</td>\n", "      <td>60.0</td>\n", "      <td>9550</td>\n", "      <td>Pave</td>\n", "      <td>NaN</td>\n", "      <td>IR1</td>\n", "      <td>Lvl</td>\n", "      <td>AllPub</td>\n", "      <td>Corner</td>\n", "      <td>Gtl</td>\n", "      <td>Crawfor</td>\n", "      <td>Norm</td>\n", "      <td>Norm</td>\n", "      <td>1Fam</td>\n", "      <td>2Story</td>\n", "      <td>7</td>\n", "      <td>5</td>\n", "      <td>1915</td>\n", "      <td>1970</td>\n", "      <td>Gable</td>\n", "      <td>CompShg</td>\n", "      <td>Wd Sdng</td>\n", "      <td>Wd Shng</td>\n", "      <td>NaN</td>\n", "      <td>0.0</td>\n", "      <td>TA</td>\n", "      <td>TA</td>\n", "      <td>BrkTil</td>\n", "      <td>TA</td>\n", "      <td>Gd</td>\n", "      <td>No</td>\n", "      <td>ALQ</td>\n", "      <td>216</td>\n", "      <td>Unf</td>\n", "      <td>0</td>\n", "      <td>540</td>\n", "      <td>756</td>\n", "      <td>GasA</td>\n", "      <td>Gd</td>\n", "      <td>Y</td>\n", "      <td>SBrkr</td>\n", "      <td>961</td>\n", "      <td>756</td>\n", "      <td>0</td>\n", "      <td>1717</td>\n", "      <td>1</td>\n", "      <td>0</td>\n", "      <td>1</td>\n", "      <td>0</td>\n", "      <td>3</td>\n", "      <td>1</td>\n", "      <td>Gd</td>\n", "      <td>7</td>\n", "      <td>Typ</td>\n", "      <td>1</td>\n", "      <td>Gd</td>\n", "      <td>Detchd</td>\n", "      <td>1998.0</td>\n", "      <td>Unf</td>\n", "      <td>3</td>\n", "      <td>642</td>\n", "      <td>TA</td>\n", "      <td>TA</td>\n", "      <td>Y</td>\n", "      <td>0</td>\n", "      <td>35</td>\n", "      <td>272</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>0</td>\n", "      <td>2</td>\n", "      <td>2006</td>\n", "      <td>WD</td>\n", "      <td>Abnorml</td>\n", "      <td>140000</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>5</td>\n", "      <td>60</td>\n", "      <td>RL</td>\n", "      <td>84.0</td>\n", "      <td>14260</td>\n", "      <td>Pave</td>\n", "      <td>NaN</td>\n", "      <td>IR1</td>\n", "      <td>Lvl</td>\n", "      <td>AllPub</td>\n", "      <td>FR2</td>\n", "      <td>Gtl</td>\n", "      <td>NoRidge</td>\n", "      <td>Norm</td>\n", "      <td>Norm</td>\n", "      <td>1Fam</td>\n", "      <td>2Story</td>\n", "      <td>8</td>\n", "      <td>5</td>\n", "      <td>2000</td>\n", "      <td>2000</td>\n", "      <td>Gable</td>\n", "      <td>CompShg</td>\n", "      <td>VinylSd</td>\n", "      <td>VinylSd</td>\n", "      <td>BrkFace</td>\n", "      <td>350.0</td>\n", "      <td>Gd</td>\n", "      <td>TA</td>\n", "      <td>PConc</td>\n", "      <td>Gd</td>\n", "      <td>TA</td>\n", "      <td>Av</td>\n", "      <td>GLQ</td>\n", "      <td>655</td>\n", "      <td>Unf</td>\n", "      <td>0</td>\n", "      <td>490</td>\n", "      <td>1145</td>\n", "      <td>GasA</td>\n", "      <td>Ex</td>\n", "      <td>Y</td>\n", "      <td>SBrkr</td>\n", "      <td>1145</td>\n", "      <td>1053</td>\n", "      <td>0</td>\n", "      <td>2198</td>\n", "      <td>1</td>\n", "      <td>0</td>\n", "      <td>2</td>\n", "      <td>1</td>\n", "      <td>4</td>\n", "      <td>1</td>\n", "      <td>Gd</td>\n", "      <td>9</td>\n", "      <td>Typ</td>\n", "      <td>1</td>\n", "      <td>TA</td>\n", "      <td>Attchd</td>\n", "      <td>2000.0</td>\n", "      <td>RFn</td>\n", "      <td>3</td>\n", "      <td>836</td>\n", "      <td>TA</td>\n", "      <td>TA</td>\n", "      <td>Y</td>\n", "      <td>192</td>\n", "      <td>84</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>0</td>\n", "      <td>12</td>\n", "      <td>2008</td>\n", "      <td>WD</td>\n", "      <td>Normal</td>\n", "      <td>250000</td>\n", "    </tr>\n", "    <tr>\n", "      <th>...</th>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1455</th>\n", "      <td>1456</td>\n", "      <td>60</td>\n", "      <td>RL</td>\n", "      <td>62.0</td>\n", "      <td>7917</td>\n", "      <td>Pave</td>\n", "      <td>NaN</td>\n", "      <td>Reg</td>\n", "      <td>Lvl</td>\n", "      <td>AllPub</td>\n", "      <td>Inside</td>\n", "      <td>Gtl</td>\n", "      <td><PERSON></td>\n", "      <td>Norm</td>\n", "      <td>Norm</td>\n", "      <td>1Fam</td>\n", "      <td>2Story</td>\n", "      <td>6</td>\n", "      <td>5</td>\n", "      <td>1999</td>\n", "      <td>2000</td>\n", "      <td>Gable</td>\n", "      <td>CompShg</td>\n", "      <td>VinylSd</td>\n", "      <td>VinylSd</td>\n", "      <td>NaN</td>\n", "      <td>0.0</td>\n", "      <td>TA</td>\n", "      <td>TA</td>\n", "      <td>PConc</td>\n", "      <td>Gd</td>\n", "      <td>TA</td>\n", "      <td>No</td>\n", "      <td>Unf</td>\n", "      <td>0</td>\n", "      <td>Unf</td>\n", "      <td>0</td>\n", "      <td>953</td>\n", "      <td>953</td>\n", "      <td>GasA</td>\n", "      <td>Ex</td>\n", "      <td>Y</td>\n", "      <td>SBrkr</td>\n", "      <td>953</td>\n", "      <td>694</td>\n", "      <td>0</td>\n", "      <td>1647</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>2</td>\n", "      <td>1</td>\n", "      <td>3</td>\n", "      <td>1</td>\n", "      <td>TA</td>\n", "      <td>7</td>\n", "      <td>Typ</td>\n", "      <td>1</td>\n", "      <td>TA</td>\n", "      <td>Attchd</td>\n", "      <td>1999.0</td>\n", "      <td>RFn</td>\n", "      <td>2</td>\n", "      <td>460</td>\n", "      <td>TA</td>\n", "      <td>TA</td>\n", "      <td>Y</td>\n", "      <td>0</td>\n", "      <td>40</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>0</td>\n", "      <td>8</td>\n", "      <td>2007</td>\n", "      <td>WD</td>\n", "      <td>Normal</td>\n", "      <td>175000</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1456</th>\n", "      <td>1457</td>\n", "      <td>20</td>\n", "      <td>RL</td>\n", "      <td>85.0</td>\n", "      <td>13175</td>\n", "      <td>Pave</td>\n", "      <td>NaN</td>\n", "      <td>Reg</td>\n", "      <td>Lvl</td>\n", "      <td>AllPub</td>\n", "      <td>Inside</td>\n", "      <td>Gtl</td>\n", "      <td>NWAmes</td>\n", "      <td>Norm</td>\n", "      <td>Norm</td>\n", "      <td>1Fam</td>\n", "      <td>1Story</td>\n", "      <td>6</td>\n", "      <td>6</td>\n", "      <td>1978</td>\n", "      <td>1988</td>\n", "      <td>Gable</td>\n", "      <td>CompShg</td>\n", "      <td>Plywood</td>\n", "      <td>Plywood</td>\n", "      <td>Stone</td>\n", "      <td>119.0</td>\n", "      <td>TA</td>\n", "      <td>TA</td>\n", "      <td>CBlock</td>\n", "      <td>Gd</td>\n", "      <td>TA</td>\n", "      <td>No</td>\n", "      <td>ALQ</td>\n", "      <td>790</td>\n", "      <td>Rec</td>\n", "      <td>163</td>\n", "      <td>589</td>\n", "      <td>1542</td>\n", "      <td>GasA</td>\n", "      <td>TA</td>\n", "      <td>Y</td>\n", "      <td>SBrkr</td>\n", "      <td>2073</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>2073</td>\n", "      <td>1</td>\n", "      <td>0</td>\n", "      <td>2</td>\n", "      <td>0</td>\n", "      <td>3</td>\n", "      <td>1</td>\n", "      <td>TA</td>\n", "      <td>7</td>\n", "      <td>Min1</td>\n", "      <td>2</td>\n", "      <td>TA</td>\n", "      <td>Attchd</td>\n", "      <td>1978.0</td>\n", "      <td>Unf</td>\n", "      <td>2</td>\n", "      <td>500</td>\n", "      <td>TA</td>\n", "      <td>TA</td>\n", "      <td>Y</td>\n", "      <td>349</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>NaN</td>\n", "      <td>MnPrv</td>\n", "      <td>NaN</td>\n", "      <td>0</td>\n", "      <td>2</td>\n", "      <td>2010</td>\n", "      <td>WD</td>\n", "      <td>Normal</td>\n", "      <td>210000</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1457</th>\n", "      <td>1458</td>\n", "      <td>70</td>\n", "      <td>RL</td>\n", "      <td>66.0</td>\n", "      <td>9042</td>\n", "      <td>Pave</td>\n", "      <td>NaN</td>\n", "      <td>Reg</td>\n", "      <td>Lvl</td>\n", "      <td>AllPub</td>\n", "      <td>Inside</td>\n", "      <td>Gtl</td>\n", "      <td>Crawfor</td>\n", "      <td>Norm</td>\n", "      <td>Norm</td>\n", "      <td>1Fam</td>\n", "      <td>2Story</td>\n", "      <td>7</td>\n", "      <td>9</td>\n", "      <td>1941</td>\n", "      <td>2006</td>\n", "      <td>Gable</td>\n", "      <td>CompShg</td>\n", "      <td>CemntBd</td>\n", "      <td>CmentBd</td>\n", "      <td>NaN</td>\n", "      <td>0.0</td>\n", "      <td>Ex</td>\n", "      <td>Gd</td>\n", "      <td>Stone</td>\n", "      <td>TA</td>\n", "      <td>Gd</td>\n", "      <td>No</td>\n", "      <td>GLQ</td>\n", "      <td>275</td>\n", "      <td>Unf</td>\n", "      <td>0</td>\n", "      <td>877</td>\n", "      <td>1152</td>\n", "      <td>GasA</td>\n", "      <td>Ex</td>\n", "      <td>Y</td>\n", "      <td>SBrkr</td>\n", "      <td>1188</td>\n", "      <td>1152</td>\n", "      <td>0</td>\n", "      <td>2340</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>2</td>\n", "      <td>0</td>\n", "      <td>4</td>\n", "      <td>1</td>\n", "      <td>Gd</td>\n", "      <td>9</td>\n", "      <td>Typ</td>\n", "      <td>2</td>\n", "      <td>Gd</td>\n", "      <td>Attchd</td>\n", "      <td>1941.0</td>\n", "      <td>RFn</td>\n", "      <td>1</td>\n", "      <td>252</td>\n", "      <td>TA</td>\n", "      <td>TA</td>\n", "      <td>Y</td>\n", "      <td>0</td>\n", "      <td>60</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>NaN</td>\n", "      <td>GdPrv</td>\n", "      <td>Shed</td>\n", "      <td>2500</td>\n", "      <td>5</td>\n", "      <td>2010</td>\n", "      <td>WD</td>\n", "      <td>Normal</td>\n", "      <td>266500</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1458</th>\n", "      <td>1459</td>\n", "      <td>20</td>\n", "      <td>RL</td>\n", "      <td>68.0</td>\n", "      <td>9717</td>\n", "      <td>Pave</td>\n", "      <td>NaN</td>\n", "      <td>Reg</td>\n", "      <td>Lvl</td>\n", "      <td>AllPub</td>\n", "      <td>Inside</td>\n", "      <td>Gtl</td>\n", "      <td>NAmes</td>\n", "      <td>Norm</td>\n", "      <td>Norm</td>\n", "      <td>1Fam</td>\n", "      <td>1Story</td>\n", "      <td>5</td>\n", "      <td>6</td>\n", "      <td>1950</td>\n", "      <td>1996</td>\n", "      <td>Hip</td>\n", "      <td>CompShg</td>\n", "      <td>MetalSd</td>\n", "      <td>MetalSd</td>\n", "      <td>NaN</td>\n", "      <td>0.0</td>\n", "      <td>TA</td>\n", "      <td>TA</td>\n", "      <td>CBlock</td>\n", "      <td>TA</td>\n", "      <td>TA</td>\n", "      <td>Mn</td>\n", "      <td>GLQ</td>\n", "      <td>49</td>\n", "      <td>Rec</td>\n", "      <td>1029</td>\n", "      <td>0</td>\n", "      <td>1078</td>\n", "      <td>GasA</td>\n", "      <td>Gd</td>\n", "      <td>Y</td>\n", "      <td>FuseA</td>\n", "      <td>1078</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>1078</td>\n", "      <td>1</td>\n", "      <td>0</td>\n", "      <td>1</td>\n", "      <td>0</td>\n", "      <td>2</td>\n", "      <td>1</td>\n", "      <td>Gd</td>\n", "      <td>5</td>\n", "      <td>Typ</td>\n", "      <td>0</td>\n", "      <td>NaN</td>\n", "      <td>Attchd</td>\n", "      <td>1950.0</td>\n", "      <td>Unf</td>\n", "      <td>1</td>\n", "      <td>240</td>\n", "      <td>TA</td>\n", "      <td>TA</td>\n", "      <td>Y</td>\n", "      <td>366</td>\n", "      <td>0</td>\n", "      <td>112</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>0</td>\n", "      <td>4</td>\n", "      <td>2010</td>\n", "      <td>WD</td>\n", "      <td>Normal</td>\n", "      <td>142125</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1459</th>\n", "      <td>1460</td>\n", "      <td>20</td>\n", "      <td>RL</td>\n", "      <td>75.0</td>\n", "      <td>9937</td>\n", "      <td>Pave</td>\n", "      <td>NaN</td>\n", "      <td>Reg</td>\n", "      <td>Lvl</td>\n", "      <td>AllPub</td>\n", "      <td>Inside</td>\n", "      <td>Gtl</td>\n", "      <td><PERSON></td>\n", "      <td>Norm</td>\n", "      <td>Norm</td>\n", "      <td>1Fam</td>\n", "      <td>1Story</td>\n", "      <td>5</td>\n", "      <td>6</td>\n", "      <td>1965</td>\n", "      <td>1965</td>\n", "      <td>Gable</td>\n", "      <td>CompShg</td>\n", "      <td>HdBoard</td>\n", "      <td>HdBoard</td>\n", "      <td>NaN</td>\n", "      <td>0.0</td>\n", "      <td>Gd</td>\n", "      <td>TA</td>\n", "      <td>CBlock</td>\n", "      <td>TA</td>\n", "      <td>TA</td>\n", "      <td>No</td>\n", "      <td>BLQ</td>\n", "      <td>830</td>\n", "      <td>LwQ</td>\n", "      <td>290</td>\n", "      <td>136</td>\n", "      <td>1256</td>\n", "      <td>GasA</td>\n", "      <td>Gd</td>\n", "      <td>Y</td>\n", "      <td>SBrkr</td>\n", "      <td>1256</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>1256</td>\n", "      <td>1</td>\n", "      <td>0</td>\n", "      <td>1</td>\n", "      <td>1</td>\n", "      <td>3</td>\n", "      <td>1</td>\n", "      <td>TA</td>\n", "      <td>6</td>\n", "      <td>Typ</td>\n", "      <td>0</td>\n", "      <td>NaN</td>\n", "      <td>Attchd</td>\n", "      <td>1965.0</td>\n", "      <td>Fin</td>\n", "      <td>1</td>\n", "      <td>276</td>\n", "      <td>TA</td>\n", "      <td>TA</td>\n", "      <td>Y</td>\n", "      <td>736</td>\n", "      <td>68</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>0</td>\n", "      <td>6</td>\n", "      <td>2008</td>\n", "      <td>WD</td>\n", "      <td>Normal</td>\n", "      <td>147500</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "<p>1460 rows × 81 columns</p>\n", "</div>\n", "    <div class=\"colab-df-buttons\">\n", "\n", "  <div class=\"colab-df-container\">\n", "    <button class=\"colab-df-convert\" onclick=\"convertToInteractive('df-efa46eda-c344-45a8-af30-0b15c51ed25f')\"\n", "            title=\"Convert this dataframe to an interactive table.\"\n", "            style=\"display:none;\">\n", "\n", "  <svg xmlns=\"http://www.w3.org/2000/svg\" height=\"24px\" viewBox=\"0 -960 960 960\">\n", "    <path d=\"M120-120v-720h720v720H120Zm60-500h600v-160H180v160Zm220 220h160v-160H400v160Zm0 220h160v-160H400v160ZM180-400h160v-160H180v160Zm440 0h160v-160H620v160ZM180-180h160v-160H180v160Zm440 0h160v-160H620v160Z\"/>\n", "  </svg>\n", "    </button>\n", "\n", "  <style>\n", "    .colab-df-container {\n", "      display:flex;\n", "      gap: 12px;\n", "    }\n", "\n", "    .colab-df-convert {\n", "      background-color: #E8F0FE;\n", "      border: none;\n", "      border-radius: 50%;\n", "      cursor: pointer;\n", "      display: none;\n", "      fill: #1967D2;\n", "      height: 32px;\n", "      padding: 0 0 0 0;\n", "      width: 32px;\n", "    }\n", "\n", "    .colab-df-convert:hover {\n", "      background-color: #E2EBFA;\n", "      box-shadow: 0px 1px 2px rgba(60, 64, 67, 0.3), 0px 1px 3px 1px rgba(60, 64, 67, 0.15);\n", "      fill: #174EA6;\n", "    }\n", "\n", "    .colab-df-buttons div {\n", "      margin-bottom: 4px;\n", "    }\n", "\n", "    [theme=dark] .colab-df-convert {\n", "      background-color: #3B4455;\n", "      fill: #D2E3FC;\n", "    }\n", "\n", "    [theme=dark] .colab-df-convert:hover {\n", "      background-color: #434B5C;\n", "      box-shadow: 0px 1px 3px 1px rgba(0, 0, 0, 0.15);\n", "      filter: drop-shadow(0px 1px 2px rgba(0, 0, 0, 0.3));\n", "      fill: #FFFFFF;\n", "    }\n", "  </style>\n", "\n", "    <script>\n", "      const buttonEl =\n", "        document.querySelector('#df-efa46eda-c344-45a8-af30-0b15c51ed25f button.colab-df-convert');\n", "      buttonEl.style.display =\n", "        google.colab.kernel.accessAllowed ? 'block' : 'none';\n", "\n", "      async function convertToInteractive(key) {\n", "        const element = document.querySelector('#df-efa46eda-c344-45a8-af30-0b15c51ed25f');\n", "        const dataTable =\n", "          await google.colab.kernel.invokeFunction('convertToInteractive',\n", "                                                    [key], {});\n", "        if (!dataTable) return;\n", "\n", "        const docLinkHtml = 'Like what you see? Visit the ' +\n", "          '<a target=\"_blank\" href=https://colab.research.google.com/notebooks/data_table.ipynb>data table notebook</a>'\n", "          + ' to learn more about interactive tables.';\n", "        element.innerHTML = '';\n", "        dataTable['output_type'] = 'display_data';\n", "        await google.colab.output.renderOutput(dataTable, element);\n", "        const docLink = document.createElement('div');\n", "        docLink.innerHTML = docLinkHtml;\n", "        element.appendChild(docLink);\n", "      }\n", "    </script>\n", "  </div>\n", "\n", "\n", "    <div id=\"df-767eccfe-726c-4145-bf5f-c3db7768cd46\">\n", "      <button class=\"colab-df-quickchart\" onclick=\"quickchart('df-767eccfe-726c-4145-bf5f-c3db7768cd46')\"\n", "                title=\"Suggest charts\"\n", "                style=\"display:none;\">\n", "\n", "<svg xmlns=\"http://www.w3.org/2000/svg\" height=\"24px\"viewBox=\"0 0 24 24\"\n", "     width=\"24px\">\n", "    <g>\n", "        <path d=\"M19 3H5c-1.1 0-2 .9-2 2v14c0 1.1.9 2 2 2h14c1.1 0 2-.9 2-2V5c0-1.1-.9-2-2-2zM9 17H7v-7h2v7zm4 0h-2V7h2v10zm4 0h-2v-4h2v4z\"/>\n", "    </g>\n", "</svg>\n", "      </button>\n", "\n", "<style>\n", "  .colab-df-quickchart {\n", "      --bg-color: #E8F0FE;\n", "      --fill-color: #1967D2;\n", "      --hover-bg-color: #E2EBFA;\n", "      --hover-fill-color: #174EA6;\n", "      --disabled-fill-color: #AAA;\n", "      --disabled-bg-color: #DDD;\n", "  }\n", "\n", "  [theme=dark] .colab-df-quickchart {\n", "      --bg-color: #3B4455;\n", "      --fill-color: #D2E3FC;\n", "      --hover-bg-color: #434B5C;\n", "      --hover-fill-color: #FFFFFF;\n", "      --disabled-bg-color: #3B4455;\n", "      --disabled-fill-color: #666;\n", "  }\n", "\n", "  .colab-df-quickchart {\n", "    background-color: var(--bg-color);\n", "    border: none;\n", "    border-radius: 50%;\n", "    cursor: pointer;\n", "    display: none;\n", "    fill: var(--fill-color);\n", "    height: 32px;\n", "    padding: 0;\n", "    width: 32px;\n", "  }\n", "\n", "  .colab-df-quickchart:hover {\n", "    background-color: var(--hover-bg-color);\n", "    box-shadow: 0 1px 2px rgba(60, 64, 67, 0.3), 0 1px 3px 1px rgba(60, 64, 67, 0.15);\n", "    fill: var(--button-hover-fill-color);\n", "  }\n", "\n", "  .colab-df-quickchart-complete:disabled,\n", "  .colab-df-quickchart-complete:disabled:hover {\n", "    background-color: var(--disabled-bg-color);\n", "    fill: var(--disabled-fill-color);\n", "    box-shadow: none;\n", "  }\n", "\n", "  .colab-df-spinner {\n", "    border: 2px solid var(--fill-color);\n", "    border-color: transparent;\n", "    border-bottom-color: var(--fill-color);\n", "    animation:\n", "      spin 1s steps(1) infinite;\n", "  }\n", "\n", "  @keyframes spin {\n", "    0% {\n", "      border-color: transparent;\n", "      border-bottom-color: var(--fill-color);\n", "      border-left-color: var(--fill-color);\n", "    }\n", "    20% {\n", "      border-color: transparent;\n", "      border-left-color: var(--fill-color);\n", "      border-top-color: var(--fill-color);\n", "    }\n", "    30% {\n", "      border-color: transparent;\n", "      border-left-color: var(--fill-color);\n", "      border-top-color: var(--fill-color);\n", "      border-right-color: var(--fill-color);\n", "    }\n", "    40% {\n", "      border-color: transparent;\n", "      border-right-color: var(--fill-color);\n", "      border-top-color: var(--fill-color);\n", "    }\n", "    60% {\n", "      border-color: transparent;\n", "      border-right-color: var(--fill-color);\n", "    }\n", "    80% {\n", "      border-color: transparent;\n", "      border-right-color: var(--fill-color);\n", "      border-bottom-color: var(--fill-color);\n", "    }\n", "    90% {\n", "      border-color: transparent;\n", "      border-bottom-color: var(--fill-color);\n", "    }\n", "  }\n", "</style>\n", "\n", "      <script>\n", "        async function quickchart(key) {\n", "          const quickchartButtonEl =\n", "            document.querySelector('#' + key + ' button');\n", "          quickchartButtonEl.disabled = true;  // To prevent multiple clicks.\n", "          quickchartButtonEl.classList.add('colab-df-spinner');\n", "          try {\n", "            const charts = await google.colab.kernel.invokeFunction(\n", "                'suggest<PERSON><PERSON>s', [key], {});\n", "          } catch (error) {\n", "            console.error('Error during call to suggest<PERSON>harts:', error);\n", "          }\n", "          quickchartButtonEl.classList.remove('colab-df-spinner');\n", "          quickchartButtonEl.classList.add('colab-df-quickchart-complete');\n", "        }\n", "        (() => {\n", "          let quickchartButtonEl =\n", "            document.querySelector('#df-767eccfe-726c-4145-bf5f-c3db7768cd46 button');\n", "          quickchartButtonEl.style.display =\n", "            google.colab.kernel.accessAllowed ? 'block' : 'none';\n", "        })();\n", "      </script>\n", "    </div>\n", "\n", "  <div id=\"id_840ca908-18b7-46aa-9680-a54b1d3acb87\">\n", "    <style>\n", "      .colab-df-generate {\n", "        background-color: #E8F0FE;\n", "        border: none;\n", "        border-radius: 50%;\n", "        cursor: pointer;\n", "        display: none;\n", "        fill: #1967D2;\n", "        height: 32px;\n", "        padding: 0 0 0 0;\n", "        width: 32px;\n", "      }\n", "\n", "      .colab-df-generate:hover {\n", "        background-color: #E2EBFA;\n", "        box-shadow: 0px 1px 2px rgba(60, 64, 67, 0.3), 0px 1px 3px 1px rgba(60, 64, 67, 0.15);\n", "        fill: #174EA6;\n", "      }\n", "\n", "      [theme=dark] .colab-df-generate {\n", "        background-color: #3B4455;\n", "        fill: #D2E3FC;\n", "      }\n", "\n", "      [theme=dark] .colab-df-generate:hover {\n", "        background-color: #434B5C;\n", "        box-shadow: 0px 1px 3px 1px rgba(0, 0, 0, 0.15);\n", "        filter: drop-shadow(0px 1px 2px rgba(0, 0, 0, 0.3));\n", "        fill: #FFFFFF;\n", "      }\n", "    </style>\n", "    <button class=\"colab-df-generate\" onclick=\"generateWithVariable('df_new')\"\n", "            title=\"Generate code using this dataframe.\"\n", "            style=\"display:none;\">\n", "\n", "  <svg xmlns=\"http://www.w3.org/2000/svg\" height=\"24px\"viewBox=\"0 0 24 24\"\n", "       width=\"24px\">\n", "    <path d=\"M7,19H8.4L18.45,9,17,7.55,7,17.6ZM5,21V16.75L18.45,3.32a2,2,0,0,1,2.83,0l1.4,1.43a1.91,1.91,0,0,1,.58,1.4,1.91,1.91,0,0,1-.58,1.4L9.25,21ZM18.45,9,17,7.55Zm-12,3A5.31,5.31,0,0,0,4.9,8.1,5.31,5.31,0,0,0,1,6.5,5.31,5.31,0,0,0,4.9,4.9,5.31,5.31,0,0,0,6.5,1,5.31,5.31,0,0,0,8.1,4.9,5.31,5.31,0,0,0,12,6.5,5.46,5.46,0,0,0,6.5,12Z\"/>\n", "  </svg>\n", "    </button>\n", "    <script>\n", "      (() => {\n", "      const buttonEl =\n", "        document.querySelector('#id_840ca908-18b7-46aa-9680-a54b1d3acb87 button.colab-df-generate');\n", "      buttonEl.style.display =\n", "        google.colab.kernel.accessAllowed ? 'block' : 'none';\n", "\n", "      buttonEl.onclick = () => {\n", "        google.colab.notebook.generateWithVariable('df_new');\n", "      }\n", "      })();\n", "    </script>\n", "  </div>\n", "\n", "    </div>\n", "  </div>\n"], "application/vnd.google.colaboratory.intrinsic+json": {"type": "dataframe", "variable_name": "df_new"}}, "metadata": {}, "execution_count": 80}]}, {"cell_type": "code", "source": ["# --- Missing Value Imputation ---\n", "# Categorical features: <PERSON>ll <PERSON> with 'None' or 'NA' indicating absence\n", "cat_cols_fill_none = [\n", "    'Alley', 'BsmtQual', 'BsmtCond', 'BsmtExposure', 'BsmtFinType1',\n", "    'BsmtFinType2', 'FireplaceQu', 'GarageType', 'GarageFinish',\n", "    'GarageQual', 'GarageCond', 'PoolQC', '<PERSON>ce', 'MiscFeature',\n", "    'MasVnrType'\n", "]\n", "for col in cat_cols_fill_none:\n", "  if col in df.columns:\n", "    df[col] = df[col].fillna('None')\n", "cat_cols_fill_none"], "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "A8fMmywfp_hW", "outputId": "d4553ca0-bbd5-4f1b-81db-676c6b1c5dbe"}, "execution_count": 81, "outputs": [{"output_type": "execute_result", "data": {"text/plain": ["['Alley',\n", " 'BsmtQual',\n", " 'BsmtCond',\n", " 'BsmtExposure',\n", " 'BsmtFinType1',\n", " 'BsmtFinType2',\n", " 'FireplaceQu',\n", " 'GarageType',\n", " 'GarageF<PERSON>sh',\n", " 'GarageQual',\n", " 'GarageCond',\n", " 'PoolQC',\n", " '<PERSON><PERSON>',\n", " 'MiscFeature',\n", " 'MasVnrType']"]}, "metadata": {}, "execution_count": 81}]}, {"cell_type": "code", "source": ["# Numerical features: Fill NaN appropriately\n", "# LotFrontage: Impute with neighborhood median\n", "if 'LotFrontage' in df.columns and 'Neighborhood' in df.columns:\n", "    df['LotFrontage'] = df.groupby('Neighborhood')['LotFrontage'].transform(lambda x: x.fillna(x.median()))\n", "    # Fill any remaining NaNs (if a neighborhood had all NaNs) with global median\n", "    df['LotFrontage'] = df['LotFrontage'].fillna(df['LotFrontage'].median())\n", "    print(f\"LotFrontage imputed. Remaining NaNs: {df['LotFrontage'].isnull().sum()}\")"], "metadata": {"id": "CqlnHnf3p_m0"}, "execution_count": 82, "outputs": []}, {"cell_type": "code", "source": ["# GarageYrBuilt: Impute with 0 for no garage, maybe YearBuilt otherwise? # GarageYrBlt: Impute with 0 for no garage, maybe YearBuilt otherwise? For simplicity, fill with 0 for now.\n", "if 'GarageYrBlt' in df.columns:\n", "    df['GarageYrBlt'] = df['GarageYrBlt'].fillna(0)"], "metadata": {"id": "0xpMWq3Qp_sb"}, "execution_count": 83, "outputs": []}, {"cell_type": "code", "source": ["# MasVnrArea: Fill with 0\n", "if 'MasVnrArea' in df.columns:\n", "    df['MasVnrArea'] = df['MasVnrArea'].fillna(0)"], "metadata": {"id": "QdpOwanuy5Hk"}, "execution_count": 84, "outputs": []}, {"cell_type": "code", "source": ["# Electrical: Fill with mode (most frequent value)\n", "if 'Electrical' in df.columns:\n", "    df['Electrical'] = df['Electrical'].fillna(df['Electrical'].mode()[0])"], "metadata": {"id": "CWLDrpkyy5Nb"}, "execution_count": 85, "outputs": []}, {"cell_type": "code", "source": ["# Basement numerical NaNs: Fill with 0\n", "bsmt_num_cols_fill_zero = ['BsmtFinSF1', 'BsmtFinSF2', 'BsmtUnfSF', 'TotalBsmtSF', 'BsmtFullBath', 'BsmtHalfBath']\n", "for col in bsmt_num_cols_fill_zero:\n", "    if col in df.columns:\n", "        df[col] = df[col].fillna(0)\n", "bsmt_num_cols_fill_zero"], "metadata": {"id": "u6iIAOU4y5Uj", "colab": {"base_uri": "https://localhost:8080/"}, "outputId": "56a4d603-3190-4016-922e-4701b61d4f39"}, "execution_count": 86, "outputs": [{"output_type": "execute_result", "data": {"text/plain": ["['BsmtFinSF1',\n", " 'BsmtFinSF2',\n", " 'BsmtUnfSF',\n", " 'TotalBsmtSF',\n", " 'BsmtFullBath',\n", " 'BsmtHalfBath']"]}, "metadata": {}, "execution_count": 86}]}, {"cell_type": "code", "source": ["# Garage numerical NaNs: Fill with 0\n", "garage_num_cols_fill_zero = ['GarageCars', 'GarageArea']\n", "for col in garage_num_cols_fill_zero:\n", "    if col in df.columns:\n", "        df[col] = df[col].fillna(0)\n", "garage_num_cols_fill_zero"], "metadata": {"id": "jtZMYSisy5bt", "colab": {"base_uri": "https://localhost:8080/"}, "outputId": "2451bc32-9476-4c54-c022-cc83530ec5f8"}, "execution_count": 87, "outputs": [{"output_type": "execute_result", "data": {"text/plain": ["['GarageCars', 'GarageArea']"]}, "metadata": {}, "execution_count": 87}]}, {"cell_type": "code", "source": ["# Check remaining NaNs\n", "print(f\"Total remaining NaNs after imputation: {df.isnull().sum().sum()}\")"], "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "5thNkhc4y5ie", "outputId": "82a43c52-46ce-4a00-c2f5-b48526a42330"}, "execution_count": 88, "outputs": [{"output_type": "stream", "name": "stdout", "text": ["Total remaining NaNs after imputation: 7829\n"]}]}, {"cell_type": "code", "source": ["# Check for specific column\n", "print(df.isnull().sum()[df.isnull().sum() > 0])"], "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "jyVsoInuy5vl", "outputId": "f06522f4-d661-45da-9110-faaec623e41e"}, "execution_count": 89, "outputs": [{"output_type": "stream", "name": "stdout", "text": ["lotfrontage      259\n", "alley           1369\n", "masvnrtype       872\n", "masvnrarea         8\n", "bsmtqual          37\n", "bsmtcond          37\n", "bsmtexposure      38\n", "bsmtfintype1      37\n", "bsmtfintype2      38\n", "electrical         1\n", "fireplacequ      690\n", "garagetype        81\n", "garageyrblt       81\n", "garagefinish      81\n", "garagequal        81\n", "garagecond        81\n", "poolqc          1453\n", "fence           1179\n", "miscfeature     1406\n", "dtype: int64\n"]}]}, {"cell_type": "markdown", "source": ["# Feature Engineering"], "metadata": {"id": "GAzJM5Lv2zv4"}}, {"cell_type": "code", "source": ["# print(\"--- Engineering Features ---\")\n", "# Total Square Footage\n", "# Use the cleaned column names (lowercase)\n", "df['TotalSF'] = df['totalbsmtsf'] + df['1stflrsf'] + df['2ndflrsf']\n", "\n", "# Total Bathrooms\n", "# Use the cleaned column names (lowercase)\n", "df['TotalBathrooms'] = df['fullbath'] + (0.5 * df['halfbath']) + df['bsmtfullbath'] + (0.5 * df['bsmthalfbath'])\n", "\n", "# Total Porch Square Footage\n", "# Use the cleaned column names (lowercase)\n", "porch_cols = ['openporchsf', 'enclosedporch', '3ssnporch', 'screenporch', 'wooddecksf']\n", "df['TotalPorchSF'] = df[porch_cols].sum(axis=1)\n", "porch_cols"], "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "dgKz0I-Ay545", "outputId": "cc9f417c-a052-4556-c11b-334f5ac8240c"}, "execution_count": 90, "outputs": [{"output_type": "execute_result", "data": {"text/plain": ["['openporchsf', 'enclosedporch', '3ssnporch', 'screenporch', 'wooddecksf']"]}, "metadata": {}, "execution_count": 90}]}, {"cell_type": "code", "source": ["# Age Features\n", "# Use the cleaned column names (lowercase)\n", "df['HouseAge'] = df['yrsold'] - df['yearbuilt']\n", "df['HouseAge'] = df['HouseAge'].apply(lambda x: max(0, x)) # No negative age\n", "df['YearsSinceRemodel'] = df['yrsold'] - df['yearremodadd']\n", "df['YearsSinceRemodel'] = df['YearsSinceRemodel'].apply(lambda x: max(0, x)) # No negative age\n", "df['GarageAge'] = df.apply(lambda row: row['yrsold'] - row['garageyrblt'] if row['garageyrblt'] > 0 else 0, axis=1)\n", "df['GarageAge'] = df['GarageAge'].apply(lambda x: max(0, x)) # No negative age"], "metadata": {"id": "ZOTMzVE6y6A4"}, "execution_count": 91, "outputs": []}, {"cell_type": "code", "source": ["# Binary Indicators\n", "df['IsNew'] = (df['HouseAge'] <= 1).astype(int)\n", "# Use the cleaned, lowercase column names\n", "df['HasPool'] = (df['poolarea'] > 0).astype(int)\n", "df['HasGarage'] = (df['garagearea'] > 0).astype(int)\n", "df['HasFireplace'] = (df['fireplaces'] > 0).astype(int)\n", "df['HasBasement'] = (df['totalbsmtsf'] > 0).astype(int)\n", "df['Has2ndFloor'] = (df['2ndflrsf'] > 0).astype(int)\n", "# Use the correct column name 'TotalPorchSF' as created in the previous cell\n", "df['HasPorch'] = (df['TotalPorchSF'] > 0).astype(int)\n", "df['HasMasVnr'] = (df['masvnrarea'] > 0).astype(int)\n", "# HasShed (from MiscFeature)\n", "if 'miscfeature' in df.columns:\n", "    df['hasshed'] = (df['miscfeature'] == 'Shed').astype(int)\n", "    print(\"Created 'hasshed' binary indicator.\")\n", "else:\n", "    df['hasshed'] = 0 # Assume no shed if column missing"], "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "WhfzPiory6Jf", "outputId": "24f6206c-7b50-4f6b-9ee5-7fea32bd5abe"}, "execution_count": 92, "outputs": [{"output_type": "stream", "name": "stdout", "text": ["Created 'hasshed' binary indicator.\n"]}]}, {"cell_type": "code", "source": ["# Interaction Features\n", "# Use the cleaned, lowercase column names\n", "df['QualxArea'] = df['overallqual'] * df['TotalSF']\n", "df['QualxAge'] = df['overallqual'] * df['HouseAge']"], "metadata": {"id": "NDo2R25Ey6SA"}, "execution_count": 93, "outputs": []}, {"cell_type": "code", "source": ["# Polynomial Features (Squared Terms)\n", "features_to_square = ['OverallQual', 'GrLivArea', 'TotalSF', 'HouseAge']\n", "for feature in features_to_square:\n", "    if feature in df.columns:\n", "        df[f'{feature}_sq'] = df[feature] ** 2\n", "features_to_square"], "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "Q3-LZ9wA5ZTI", "outputId": "51a51fa7-8181-4637-f192-eebb43242e07"}, "execution_count": 94, "outputs": [{"output_type": "execute_result", "data": {"text/plain": ["['OverallQual', 'GrLivArea', 'TotalSF', 'HouseAge']"]}, "metadata": {}, "execution_count": 94}]}, {"cell_type": "code", "source": ["# Neighborhood Price Encoding (Mean Target Encoding - potential for leakage if not done carefully, e.g., within CV)\n", "# For simplicity here, we calculate it on the whole dataset before splitting.\n", "# A more robust approach uses training fold data only or applies smoothing.\n", "# Use the cleaned, lowercase column names 'neighborhood' and 'saleprice'\n", "neighborhood_price_map = df.groupby('neighborhood')['saleprice'].mean()\n", "df['NeighborhoodPrice'] = df['neighborhood'].map(neighborhood_price_map)\n", "\n", "print(\"Feature engineering complete.\")\n", "neighborhood_price_map"], "metadata": {"colab": {"base_uri": "https://localhost:8080/", "height": 917}, "id": "03syWvBn5Zgg", "outputId": "69e55fbf-468d-42f9-ccab-d9e4350f2601"}, "execution_count": 95, "outputs": [{"output_type": "stream", "name": "stdout", "text": ["Feature engineering complete.\n"]}, {"output_type": "execute_result", "data": {"text/plain": ["neighborhood\n", "Blmngtn    194870.882353\n", "Blueste    137500.000000\n", "BrDale     104493.750000\n", "BrkSide    124834.051724\n", "ClearCr    212565.428571\n", "CollgCr    197965.773333\n", "Crawfor    210624.725490\n", "Edwards    128219.700000\n", "<PERSON>    192854.506329\n", "IDOTRR     100123.783784\n", "MeadowV     98576.470588\n", "Mitchel    156270.122449\n", "NAmes      145847.080000\n", "NPkVill    142694.444444\n", "NWAmes     189050.068493\n", "NoRidge    335295.317073\n", "NridgHt    316270.623377\n", "OldTown    128225.300885\n", "SWISU      142591.360000\n", "Sawyer     136793.135135\n", "SawyerW    186555.796610\n", "Somerst    225379.837209\n", "StoneBr    310499.000000\n", "Timber     242247.447368\n", "Veenker    238772.727273\n", "Name: saleprice, dtype: float64"], "text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>saleprice</th>\n", "    </tr>\n", "    <tr>\n", "      <th>neighborhood</th>\n", "      <th></th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>Blmngtn</th>\n", "      <td>194870.882353</td>\n", "    </tr>\n", "    <tr>\n", "      <th><PERSON><PERSON></th>\n", "      <td>137500.000000</td>\n", "    </tr>\n", "    <tr>\n", "      <th>BrDale</th>\n", "      <td>104493.750000</td>\n", "    </tr>\n", "    <tr>\n", "      <th>BrkSide</th>\n", "      <td>124834.051724</td>\n", "    </tr>\n", "    <tr>\n", "      <th>ClearCr</th>\n", "      <td>212565.428571</td>\n", "    </tr>\n", "    <tr>\n", "      <th>CollgCr</th>\n", "      <td>197965.773333</td>\n", "    </tr>\n", "    <tr>\n", "      <th>Crawfor</th>\n", "      <td>210624.725490</td>\n", "    </tr>\n", "    <tr>\n", "      <th><PERSON></th>\n", "      <td>128219.700000</td>\n", "    </tr>\n", "    <tr>\n", "      <th><PERSON></th>\n", "      <td>192854.506329</td>\n", "    </tr>\n", "    <tr>\n", "      <th>IDOTRR</th>\n", "      <td>100123.783784</td>\n", "    </tr>\n", "    <tr>\n", "      <th>MeadowV</th>\n", "      <td>98576.470588</td>\n", "    </tr>\n", "    <tr>\n", "      <th><PERSON><PERSON></th>\n", "      <td>156270.122449</td>\n", "    </tr>\n", "    <tr>\n", "      <th>NAmes</th>\n", "      <td>145847.080000</td>\n", "    </tr>\n", "    <tr>\n", "      <th>NPkVill</th>\n", "      <td>142694.444444</td>\n", "    </tr>\n", "    <tr>\n", "      <th><PERSON><PERSON></th>\n", "      <td>189050.068493</td>\n", "    </tr>\n", "    <tr>\n", "      <th>NoRidge</th>\n", "      <td>335295.317073</td>\n", "    </tr>\n", "    <tr>\n", "      <th>NridgHt</th>\n", "      <td>316270.623377</td>\n", "    </tr>\n", "    <tr>\n", "      <th>OldTown</th>\n", "      <td>128225.300885</td>\n", "    </tr>\n", "    <tr>\n", "      <th>SWISU</th>\n", "      <td>142591.360000</td>\n", "    </tr>\n", "    <tr>\n", "      <th><PERSON></th>\n", "      <td>136793.135135</td>\n", "    </tr>\n", "    <tr>\n", "      <th><PERSON><PERSON></th>\n", "      <td>186555.796610</td>\n", "    </tr>\n", "    <tr>\n", "      <th><PERSON><PERSON></th>\n", "      <td>225379.837209</td>\n", "    </tr>\n", "    <tr>\n", "      <th>StoneBr</th>\n", "      <td>310499.000000</td>\n", "    </tr>\n", "    <tr>\n", "      <th><PERSON><PERSON></th>\n", "      <td>242247.447368</td>\n", "    </tr>\n", "    <tr>\n", "      <th><PERSON><PERSON><PERSON></th>\n", "      <td>238772.727273</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div><br><label><b>dtype:</b> float64</label>"]}, "metadata": {}, "execution_count": 95}]}, {"cell_type": "code", "source": ["# Exterior Material Consolidation\n", "if 'exterior1st' in df.columns and 'exterior2nd' in df.columns:\n", "    # Map based on Exterior1st, handle potential NaNs from mapping if needed\n", "    exterior_map = {\n", "\n", "        'VinylSd': 'Vinyl', 'MetalSd': 'Metal', 'Wd Sdng': 'Wood', 'HdBoard': 'Wood',\n", "\n", "        'BrkFace': 'Brick', 'WdShing': '<PERSON>', 'CemntBd': 'Cement', 'Plywood': '<PERSON>',\n", "\n", "        'AsbShng': 'Asbestos', 'Stucco': 'Stuc<PERSON>', 'BrkComm': 'Brick', '<PERSON>phShn': 'Asphalt',\n", "\n", "        'Stone': '<PERSON>', 'ImStucc': '<PERSON>uc<PERSON>', 'CBlock': 'CBlock'\n", "\n", "        # Add other mappings if necessary based on unique values\n", "\n", "    }\n", "    df['exterior_material'] = df['exterior1st'].map(exterior_map).fillna('Other')\n", "    print(\"Consolidated exterior materials into 'exterior_material'.\")\n", "else:\n", "    print(\"Warning: Exterior1st or Exterior2nd columns not found for consolidation.\")\n", "exterior_map"], "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "5UcHqPwD5Z2_", "outputId": "1667de70-0375-4db4-cd4d-80a7dbb0bf41"}, "execution_count": 96, "outputs": [{"output_type": "stream", "name": "stdout", "text": ["Consolidated exterior materials into 'exterior_material'.\n"]}, {"output_type": "execute_result", "data": {"text/plain": ["{'VinylSd': 'Vinyl',\n", " 'MetalSd': 'Metal',\n", " 'Wd Sdng': 'Wood',\n", " 'HdBoard': 'Wood',\n", " 'BrkFace': 'Brick',\n", " 'WdShing': 'Wood',\n", " 'CemntBd': 'Cement',\n", " 'Plywood': 'Wood',\n", " 'AsbShng': 'Asbestos',\n", " 'Stucco': '<PERSON>uc<PERSON>',\n", " 'BrkComm': 'Brick',\n", " 'Asph<PERSON>hn': 'Asphalt',\n", " 'Stone': 'Stone',\n", " 'ImStucc': 'Stuc<PERSON>',\n", " 'CBlock': 'CBlock'}"]}, "metadata": {}, "execution_count": 96}]}, {"cell_type": "code", "source": ["# LandSlope Simplification\n", "if 'landslope' in df.columns:\n", "    df['landslope'] = df['landslope'].map({'Gtl': 'flat', 'Mod': 'sloped', 'Sev': 'sloped'}).fillna('flat')\n", "    print(\"Simplified 'landslope' into flat/sloped.\")\n", "df['landslope']"], "metadata": {"colab": {"base_uri": "https://localhost:8080/", "height": 476}, "id": "ccbbh8c05apw", "outputId": "addd7825-6481-4504-bee4-f9f7b6d273b6"}, "execution_count": 97, "outputs": [{"output_type": "stream", "name": "stdout", "text": ["Simplified 'landslope' into flat/sloped.\n"]}, {"output_type": "execute_result", "data": {"text/plain": ["0       flat\n", "1       flat\n", "2       flat\n", "3       flat\n", "4       flat\n", "        ... \n", "1455    flat\n", "1456    flat\n", "1457    flat\n", "1458    flat\n", "1459    flat\n", "Name: landslope, Length: 1460, dtype: object"], "text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>landslope</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>flat</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>flat</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>flat</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>flat</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>flat</td>\n", "    </tr>\n", "    <tr>\n", "      <th>...</th>\n", "      <td>...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1455</th>\n", "      <td>flat</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1456</th>\n", "      <td>flat</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1457</th>\n", "      <td>flat</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1458</th>\n", "      <td>flat</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1459</th>\n", "      <td>flat</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "<p>1460 rows × 1 columns</p>\n", "</div><br><label><b>dtype:</b> object</label>"]}, "metadata": {}, "execution_count": 97}]}, {"cell_type": "code", "source": ["# Description of LandSlope Simplification\n", "df['landslope'].describe()"], "metadata": {"colab": {"base_uri": "https://localhost:8080/", "height": 209}, "id": "gP99FmSi5bC_", "outputId": "35172673-7c60-469a-fb50-98f7b3ad866b"}, "execution_count": 98, "outputs": [{"output_type": "execute_result", "data": {"text/plain": ["count     1460\n", "unique       2\n", "top       flat\n", "freq      1382\n", "Name: landslope, dtype: object"], "text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>landslope</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>count</th>\n", "      <td>1460</td>\n", "    </tr>\n", "    <tr>\n", "      <th>unique</th>\n", "      <td>2</td>\n", "    </tr>\n", "    <tr>\n", "      <th>top</th>\n", "      <td>flat</td>\n", "    </tr>\n", "    <tr>\n", "      <th>freq</th>\n", "      <td>1382</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div><br><label><b>dtype:</b> object</label>"]}, "metadata": {}, "execution_count": 98}]}, {"cell_type": "code", "source": ["# --- Ordinal Feature Encoding ---\n", "# Define mappings (adjust based on actual values and desired order)\n", "qual_cond_map = {'Ex': 5, 'Gd': 4, 'TA': 3, 'Fa': 2, 'Po': 1, 'None': 0}\n", "bsmt_exp_map = {'Gd': 4, 'Av': 3, 'Mn': 2, 'No': 1, 'None': 0}\n", "bsmt_fin_map = {'GLQ': 6, 'ALQ': 5, 'BLQ': 4, 'Rec': 3, 'LwQ': 2, 'Unf': 1, 'None': 0}\n", "garage_fin_map = {'Fin': 3, 'RFn': 2, 'Unf': 1, 'None': 0}\n", "fence_map = {'GdPrv': 4, 'MnPrv': 3, 'GdWo': 2, 'MnWw': 1, 'None': 0}\n", "lot_shape_map = {'Reg': 0, 'IR1': 1, 'IR2': 2, 'IR3': 3} # Lower is more regular\n", "land_slope_map = {'Gtl': 0, 'Mod': 1, 'Sev': 2} # Lower is gentler slope"], "metadata": {"id": "uqkG_8455bZw"}, "execution_count": 99, "outputs": []}, {"cell_type": "code", "source": ["print(\"--- Encoding Ordinal Features ---\")\n", "ordinal_cols_maps = {\n", "    'ExterQual': qual_cond_map, 'ExterCond': qual_cond_map, 'BsmtQual': qual_cond_map,\n", "    'BsmtCond': qual_cond_map, 'HeatingQC': qual_cond_map, 'KitchenQual': qual_cond_map,\n", "    'FireplaceQu': qual_cond_map, 'GarageQual': qual_cond_map, 'GarageCond': qual_cond_map,\n", "    'PoolQC': qual_cond_map, 'BsmtExposure': bsmt_exp_map, 'BsmtFinType1': bsmt_fin_map,\n", "    'BsmtFinType2': bsmt_fin_map, 'GarageFinish': garage_fin_map, 'Fence': fence_map,\n", "    'LotShape': lot_shape_map, 'LandSlope': land_slope_map\n", "}\n", "\n", "for col, mapping in ordinal_cols_maps.items():\n", "    if col in df.columns:\n", "        df[col] = df[col].map(mapping).fillna(0) # Fill potential new NaNs introduced by mapping with 0\n", "\n", "print(\"Ordinal encoding complete.\")\n", "ordinal_cols_maps"], "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "2l1uoLiA5b5U", "outputId": "a2c729c0-e180-49d1-9387-c74d485b977c"}, "execution_count": 100, "outputs": [{"output_type": "stream", "name": "stdout", "text": ["--- Encoding Ordinal Features ---\n", "Ordinal encoding complete.\n"]}, {"output_type": "execute_result", "data": {"text/plain": ["{'ExterQual': {'Ex': 5, 'Gd': 4, 'TA': 3, 'Fa': 2, 'Po': 1, 'None': 0},\n", " 'ExterCond': {'Ex': 5, 'Gd': 4, 'TA': 3, 'Fa': 2, 'Po': 1, 'None': 0},\n", " 'BsmtQual': {'Ex': 5, 'Gd': 4, 'TA': 3, 'Fa': 2, 'Po': 1, 'None': 0},\n", " 'BsmtCond': {'Ex': 5, 'Gd': 4, 'TA': 3, 'Fa': 2, 'Po': 1, 'None': 0},\n", " 'HeatingQC': {'Ex': 5, 'Gd': 4, 'TA': 3, 'Fa': 2, 'Po': 1, 'None': 0},\n", " 'KitchenQual': {'Ex': 5, 'Gd': 4, 'TA': 3, 'Fa': 2, 'Po': 1, 'None': 0},\n", " 'FireplaceQu': {'Ex': 5, 'Gd': 4, 'TA': 3, 'Fa': 2, 'Po': 1, 'None': 0},\n", " 'GarageQual': {'Ex': 5, 'Gd': 4, 'TA': 3, 'Fa': 2, 'Po': 1, 'None': 0},\n", " 'GarageCond': {'Ex': 5, 'Gd': 4, 'TA': 3, 'Fa': 2, 'Po': 1, 'None': 0},\n", " 'PoolQC': {'Ex': 5, 'Gd': 4, 'TA': 3, 'Fa': 2, 'Po': 1, 'None': 0},\n", " 'BsmtExposure': {'Gd': 4, 'Av': 3, 'Mn': 2, 'No': 1, 'None': 0},\n", " 'BsmtFinType1': {'GLQ': 6,\n", "  'ALQ': 5,\n", "  'BLQ': 4,\n", "  'Rec': 3,\n", "  'LwQ': 2,\n", "  'Unf': 1,\n", "  'None': 0},\n", " 'BsmtFinType2': {'GLQ': 6,\n", "  'ALQ': 5,\n", "  'BLQ': 4,\n", "  'Rec': 3,\n", "  'LwQ': 2,\n", "  'Unf': 1,\n", "  'None': 0},\n", " 'GarageFinish': {'Fin': 3, 'RFn': 2, 'Unf': 1, 'None': 0},\n", " 'Fence': {'GdPrv': 4, 'MnPrv': 3, 'GdWo': 2, 'MnWw': 1, 'None': 0},\n", " 'LotShape': {'Reg': 0, 'IR1': 1, 'IR2': 2, 'IR3': 3},\n", " 'LandSlope': {'Gtl': 0, 'Mod': 1, 'Sev': 2}}"]}, "metadata": {}, "execution_count": 100}]}, {"cell_type": "code", "source": ["# --- Numerical to Categorical Conversion ---\n", "if 'MSSubClass' in df.columns:\n", "    df['MSSubClass'] = df['MSSubClass'].astype(str)\n", "    print(\"MSSubClass converted to string.\")"], "metadata": {"id": "UougHUv-5cM-"}, "execution_count": 101, "outputs": []}, {"cell_type": "code", "source": ["# --- Target Transformation ---\n", "# Log transform SalePrice to handle skewness\n", "if 'SalePrice' in df.columns:\n", "    df['SalePrice'] = np.log1p(df['SalePrice'])\n", "    print(\"SalePrice log-transformed (log1p).\")"], "metadata": {"id": "M62sgkzv5ciG"}, "execution_count": 102, "outputs": []}, {"cell_type": "code", "source": ["# --- Outlier Handling (Example) ---\n", "# IMPORTANT: Outlier removal is typically done *only* on the training set after splitting\n", "# to avoid data leakage. Applying it here to the whole dataset before splitting is for demonstration.\n", "# Consider implementing this within a cross-validation loop or after train_test_split in a real scenario.\n", "print(\"\\n--- Handling Outliers (Example - Apply carefully) ---\")\n", "initial_rows = len(df)\n", "if 'grlivarea' in df.columns:\n", "    df = df[df['grlivarea'] <= 4500]\n", "if 'lotfrontage' in df.columns:\n", "    df = df[df['lotfrontage'] <= 300]\n", "if 'lotarea' in df.columns:\n", "    df = df[df['lotarea'] <= 100000]\n", "rows_removed = initial_rows - len(df)\n", "print(f\"Removed {rows_removed} rows based on example outlier thresholds (GrLivArea, LotFrontage, LotArea).\")"], "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "yuRGW7WL5c0-", "outputId": "82e1bc61-570c-4248-c266-875a25468f4f"}, "execution_count": 103, "outputs": [{"output_type": "stream", "name": "stdout", "text": ["\n", "--- Handling Outliers (Example - Apply carefully) ---\n", "Removed 263 rows based on example outlier thresholds (GrLivArea, LotFrontage, LotArea).\n"]}]}, {"cell_type": "code", "source": ["# --- Drop Unnecessary Columns ---\n", "print(\"--- Dropping Columns ---\")\n", "# Drop original columns used in aggregations/transformations if no longer needed\n", "# Also drop columns identified as low variance, redundant, or problematic in previous analyses\n", "cols_to_drop = [\n", "    'Id', 'PID', # Identifiers\n", "    'YearBuilt', 'YearRemodAdd', 'GarageYrBlt', 'YrSold', # Used in age features\n", "    '1stFlrSF', '2ndFlrSF', 'TotalBsmtSF', # Used in TotalSF\n", "    'FullBath', 'HalfBath', 'BsmtFullBath', 'BsmtHalfBath', # Used in TotalBathrooms\n", "    'OpenPorchSF', 'EnclosedPorch', '3SsnPorch', 'ScreenPorch', 'WoodDeckSF', # Used in TotalPorchSF\n", "    'PoolArea', 'Fireplaces', # Used in binary indicators\n", "    'Alley', 'Street', 'Utilities', 'Condition2', 'RoofMatl', 'Heating', # Low variance/problematic\n", "    'LowQualFinSF', 'MiscFeature', 'MiscVal' # Low variance/problematic\n", "    'exterior1st', 'exterior2nd', # Replaced by exterior_material\n", "    'miscfeature', # Replaced by hasshed\n", "]\n", "cols_to_drop"], "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "ZRV_jHSu5dEh", "outputId": "941c713f-f755-4b99-8991-5c2771fab8fd"}, "execution_count": 104, "outputs": [{"output_type": "stream", "name": "stdout", "text": ["--- Dropping Columns ---\n"]}, {"output_type": "execute_result", "data": {"text/plain": ["['Id',\n", " 'PID',\n", " 'YearBuilt',\n", " 'YearRemodAdd',\n", " 'GarageYrBlt',\n", " 'Yr<PERSON><PERSON>',\n", " '1stFlrSF',\n", " '2ndFlrSF',\n", " 'TotalBsmtSF',\n", " 'FullBath',\n", " 'HalfBath',\n", " 'BsmtFullBath',\n", " 'BsmtHalfBath',\n", " 'OpenPorchSF',\n", " 'EnclosedPorch',\n", " '3SsnPorch',\n", " 'ScreenPorch',\n", " 'WoodDeckSF',\n", " 'PoolArea',\n", " 'Fireplaces',\n", " 'Alley',\n", " 'Street',\n", " 'Utilities',\n", " 'Condition2',\n", " 'RoofMatl',\n", " 'Heating',\n", " 'LowQualFinSF',\n", " 'MiscFeature',\n", " 'MiscValexterior1st',\n", " 'exterior2nd',\n", " 'miscfeature']"]}, "metadata": {}, "execution_count": 104}]}, {"cell_type": "code", "source": ["# Add columns that might exist and should be dropped\n", "potential_drops = ['GarageCars'] # Often collinear with GarageArea\n", "cols_to_drop.extend([col for col in potential_drops if col in df.columns])"], "metadata": {"id": "8W-MVVkU5dZw"}, "execution_count": 105, "outputs": []}, {"cell_type": "code", "source": ["# Drop columns that exist in the dataframe\n", "existing_cols_to_drop = [col for col in cols_to_drop if col in df.columns]\n", "df = df.drop(columns=existing_cols_to_drop)\n", "print(f\"Dropped {len(existing_cols_to_drop)} columns.\")"], "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "orvxqBMsp_yI", "outputId": "cdb7da3c-cf80-4c37-f20f-8769bf4f9b78"}, "execution_count": 106, "outputs": [{"output_type": "stream", "name": "stdout", "text": ["Dropped 2 columns.\n"]}]}, {"cell_type": "code", "source": ["print(f\"Preprocessing and feature engineering finished. Final shape: {df.shape}\")"], "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "VgKPTyZn-8KH", "outputId": "b08529e4-80b2-414d-a7a5-78b9ddb47383"}, "execution_count": 107, "outputs": [{"output_type": "stream", "name": "stdout", "text": ["Preprocessing and feature engineering finished. Final shape: (1197, 100)\n"]}]}, {"cell_type": "code", "source": ["# --- Feature Transformation (Skewness) ---\n", "print(\"\\n--- Handling Skewed Features ---\")\n", "# Identify numerical features (excluding the target if present)\n", "numerical_cols = df.select_dtypes(include=[np.number]).columns.tolist()\n", "if \"saleprice\" in numerical_cols: # Exclude target\n", "    numerical_cols.remove(\"saleprice\")\n", "\n", "# Calculate skewness\n", "skewness = df[numerical_cols].apply(lambda x: x.skew()).sort_values(ascending=False)\n", "print(\"Skewness of numerical features (Top 10):\")\n", "print(skewness.head(10))"], "metadata": {"id": "-wyM1uKQ-8bS", "colab": {"base_uri": "https://localhost:8080/"}, "outputId": "18416859-252b-4044-f3e5-2a1ad6fbd101"}, "execution_count": 108, "outputs": [{"output_type": "stream", "name": "stdout", "text": ["\n", "--- Handling Skewed Features ---\n", "Skewness of numerical features (Top 10):\n", "poolarea        15.577475\n", "HasPool         15.394740\n", "3ssnporch       11.224396\n", "miscval         11.127151\n", "lowqualfinsf     8.363418\n", "hasshed          5.509725\n", "bsmtfinsf2       4.504895\n", "bsmthalfbath     4.325209\n", "kitchenabvgr     4.235791\n", "screenporch      4.175522\n", "dtype: float64\n"]}]}, {"cell_type": "code", "source": ["# Apply log transformation (Log1p) to highly skewed features\n", "# Threshold can be adjusted, e.g., 0.75 or 1\n", "skew_threshold = 0.75\n", "highly_skewed_cols = skewness[abs(skewness) > skew_threshold].index.tolist()\n", "\n", "print(f\"\\nApplying log1p transformation to {len(highly_skewed_cols)} features with skewness > {skew_threshold}:\\n\")\n", "highly_skewed_cols # Uncomment to see list"], "metadata": {"id": "oRgTFuLf-80e", "colab": {"base_uri": "https://localhost:8080/"}, "outputId": "3ff899df-4a09-4dec-dbf0-f574a70bf9e2"}, "execution_count": 109, "outputs": [{"output_type": "stream", "name": "stdout", "text": ["\n", "Applying log1p transformation to 31 features with skewness > 0.75:\n", "\n"]}, {"output_type": "execute_result", "data": {"text/plain": ["['poolarea',\n", " 'HasPool',\n", " '3ssnporch',\n", " 'miscval',\n", " 'lowqualfinsf',\n", " 'hasshed',\n", " 'bsmtfinsf2',\n", " 'bsmthalfbath',\n", " 'kitchenabvgr',\n", " 'screenporch',\n", " 'lotarea',\n", " 'enclosedporch',\n", " 'masvnrarea',\n", " 'openporchsf',\n", " 'TotalSF_sq',\n", " 'IsNew',\n", " 'HouseAge_sq',\n", " 'wooddecksf',\n", " 'mssubclass',\n", " 'QualxArea',\n", " 'TotalPorchSF',\n", " 'QualxAge',\n", " 'NeighborhoodPrice',\n", " 'grlivarea',\n", " 'bsmtfinsf1',\n", " 'bsmtunfsf',\n", " '2ndflrsf',\n", " 'TotalSF',\n", " 'HasPorch',\n", " 'HasGarage',\n", " 'HasBasement']"]}, "metadata": {}, "execution_count": 109}]}, {"cell_type": "code", "source": ["for col in highly_skewed_cols:\n", "    # Ensure non-negative values before log transform if necessary\n", "    if df[col].min() < 0:\n", "        print(f\"  Warning: Column {col} has negative values, skipping log transform.\")\n", "        continue\n", "    df[col] = np.log1p(df[col])\n", "\n", "print(\"Log transformation applied to skewed features.\")\n", "print(\"\") # Add newline for spacing"], "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "yOdD1BZHyZMW", "outputId": "e9c832ff-a67c-4d45-f6f6-1e1a087b5863"}, "execution_count": 110, "outputs": [{"output_type": "stream", "name": "stdout", "text": ["Log transformation applied to skewed features.\n", "\n"]}]}, {"cell_type": "code", "source": ["# --- Outlier Handling (IQR Method) ---\n", "print(\"\\n--- Handling Outliers using IQR ---\")\n", "# IMPORTANT: Like thresholding, IQR outlier handling is best done *after* train/test split \\n\n", "# on the training data only and then applied to the test data, or within CV. \\n\n", "# Applying it here before splitting is for demonstration.\n", "\n", "# Select numerical columns again (might have changed after transforms)\n", "numerical_cols_for_outliers = df.select_dtypes(include=np.number).columns.tolist()\n", "if \"saleprice\" in numerical_cols_for_outliers:\n", "    numerical_cols_for_outliers.remove(\"saleprice\")\n", "\n", "outliers_imputed_count = 0\n", "for col in numerical_cols_for_outliers:\n", "    Q1 = df[col].quantile(0.25)\n", "    Q3 = df[col].quantile(0.75)\n", "    IQR = Q3 - Q1\n", "    lower_bound = Q1 - 1.5 * IQR\n", "    upper_bound = Q3 + 1.5 * IQR\n", "\n", "    # Identify outliers\n", "    outliers = df[(df[col] < lower_bound) | (df[col] > upper_bound)][col]\n", "    if not outliers.empty:\n", "        # Impute outliers with the median (or use winsorization)\n", "        median_val = df[col].median()\n", "        df.loc[outliers.index, col] = median_val\n", "        outliers_imputed_count += len(outliers)\n", "        # print(f\"  Imputed {len(outliers)} outliers in {col} with median {median_val:.2f}\") # Uncomment for detail\n", "\n", "print(f\"Total outliers imputed using IQR method across numerical features: {outliers_imputed_count}\")\n", "# Note: The previous threshold-based outlier removal is now redundant if IQR is used.\n", "# Consider commenting out or removing the threshold-based removal cell added previously.\n", "print(\"\") # Add newline for spacing\n"], "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "3NpfkGVtyZao", "outputId": "be773828-bac6-4ef9-81dc-41dbb884d47b"}, "execution_count": 111, "outputs": [{"output_type": "stream", "name": "stdout", "text": ["\n", "--- Handling Outliers using IQR ---\n", "Total outliers imputed using IQR method across numerical features: 2008\n", "\n"]}]}, {"cell_type": "code", "source": ["# --- Correlation-Based Feature Dropping ---\n", "print(\"\\n--- Dropping Highly Correlated Features ---\")\n", "# Select numerical columns\n", "numerical_cols_for_corr = df.select_dtypes(include=np.number).columns.tolist()\n", "if \"saleprice\" in numerical_cols_for_corr:\n", "    numerical_cols_for_corr.remove(\"saleprice\")\n", "\n", "# Calculate correlation matrix\n", "corr_matrix = df[numerical_cols_for_corr].corr().abs()\n", "\n", "# Select upper triangle of correlation matrix\n", "upper_tri = corr_matrix.where(np.triu(np.ones(corr_matrix.shape), k=1).astype(bool))\n", "\n", "# Find features with correlation greater than threshold (e.g., 0.9)\n", "corr_threshold = 0.90\n", "cols_to_drop_corr = [column for column in upper_tri.columns if any(upper_tri[column] > corr_threshold)]\n", "\n", "if cols_to_drop_corr:\n", "    print(f\"Found {len(cols_to_drop_corr)} features to drop due to high correlation (> {corr_threshold}):\")\n", "    # print(cols_to_drop_corr) # Uncomment to see list\n", "    df = df.drop(columns=cols_to_drop_corr)\n", "    print(f\"Dropped highly correlated features. New shape: {df.shape}\")\n", "else:\n", "    print(\"No features dropped based on correlation threshold.\")\n", "print(\"\") # Add newline for spacing\n"], "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "iw_LiW7XyZmF", "outputId": "537b7f9d-d967-4432-eb43-7b500b83bbea"}, "execution_count": 112, "outputs": [{"output_type": "stream", "name": "stdout", "text": ["\n", "--- Dropping Highly Correlated Features ---\n", "Found 8 features to drop due to high correlation (> 0.9):\n", "Dropped highly correlated features. New shape: (1197, 92)\n", "\n"]}]}, {"cell_type": "markdown", "source": ["# Save Preprocessed Data"], "metadata": {"id": "LKLPs9lp4gEi"}}, {"cell_type": "code", "source": ["# Define the filename for the preprocessed data\n", "preprocessed_file = 'house_price_preprocessed_data.csv'\n", "\n", "# Save the preprocessed DataFrame to a CSV file\n", "try:\n", "    df.to_csv(preprocessed_file, index=False)\n", "    print(f'Preprocessed data saved successfully to {preprocessed_file}')\n", "except Exception as e:\n", "    print(f'Error saving preprocessed data: {e}')"], "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "ucnncMFSzomN", "outputId": "986c5f67-e4a3-46be-f17c-4fc7dc905c39"}, "execution_count": 113, "outputs": [{"output_type": "stream", "name": "stdout", "text": ["Preprocessed data saved successfully to house_price_preprocessed_data.csv\n"]}]}, {"cell_type": "markdown", "source": ["# Reload Preprocessed Data"], "metadata": {"id": "UNBLF8t25PQD"}}, {"cell_type": "code", "source": ["# Read the preprocessed data back from the CSV file\n", "try:\n", "    df_loaded = pd.read_csv(preprocessed_file)\n", "    print(f'Preprocessed data reloaded successfully from {preprocessed_file}. Shape: {df_loaded.shape}')\n", "\n", "    # Display the first few rows of the reloaded data\n", "    print(\"\\nFirst 5 rows of reloaded preprocessed data:\")\n", "    try:\n", "        from IPython.display import display\n", "        display(df_loaded.head())\n", "    except ImportError:\n", "        print(df_loaded.head())\n", "\n", "    # Optional: Assign back to df if subsequent steps use 'df'\n", "    df = df_loaded.copy()\n", "\n", "except FileNotFoundError:\n", "    print(f'Error: {preprocessed_file} not found. Please ensure the saving step was successful.')\n", "    # Exit if data not loaded\n", "    exit()\n", "except Exception as e:\n", "    print(f'Error reloading preprocessed data: {e}')\n", "    # Exit if data not loaded\n", "    exit()"], "metadata": {"colab": {"base_uri": "https://localhost:8080/", "height": 281}, "id": "iq3gf-7VzouL", "outputId": "8a1ae3c5-32aa-44d4-867a-192676d03ea0"}, "execution_count": 115, "outputs": [{"output_type": "stream", "name": "stdout", "text": ["Preprocessed data reloaded successfully from house_price_preprocessed_data.csv. Shape: (1197, 92)\n", "\n", "First 5 rows of reloaded preprocessed data:\n"]}, {"output_type": "display_data", "data": {"text/plain": ["   id  mssubclass mszoning  lotfrontage   lotarea street alley lotshape  \\\n", "0   1    4.110874       RL         65.0  9.042040   Pave   NaN      Reg   \n", "1   2    3.044522       RL         80.0  9.169623   Pave   NaN      Reg   \n", "2   3    4.110874       RL         68.0  9.328212   Pave   NaN      IR1   \n", "3   4    4.262680       RL         60.0  9.164401   Pave   NaN      IR1   \n", "4   5    4.110874       RL         84.0  9.565284   Pave   NaN      IR1   \n", "\n", "  landcontour utilities lotconfig landslope neighborhood condition1  \\\n", "0         Lvl    AllPub    Inside      flat      CollgCr       Norm   \n", "1         Lvl    AllPub       FR2      flat      Veenker      Feedr   \n", "2         Lvl    AllPub    Inside      flat      CollgCr       Norm   \n", "3         Lvl    AllPub    Corner      flat      Crawfor       Norm   \n", "4         Lvl    AllPub       FR2      flat      NoRidge       Norm   \n", "\n", "  condition2 bldgtype housestyle  overallqual  overallcond  yearbuilt  \\\n", "0       Norm     1Fam     2Story            7            5       2003   \n", "1       Norm     1Fam     1Story            6            5       1976   \n", "2       Norm     1Fam     2Story            7            5       2001   \n", "3       Norm     1Fam     2Story            7            5       1915   \n", "4       Norm     1Fam     2Story            8            5       2000   \n", "\n", "   yearremodadd roofstyle roofmatl exterior1st masvnrtype  masvnrarea  \\\n", "0          2003     Gable  CompShg     VinylSd    BrkFace    5.283204   \n", "1          1976     Gable  CompShg     MetalSd        NaN    0.000000   \n", "2          2002     Gable  CompShg     VinylSd    BrkFace    5.093750   \n", "3          1970     Gable  CompShg     Wd Sdng        NaN    0.000000   \n", "4          2000     Gable  CompShg     VinylSd    BrkFace    5.860786   \n", "\n", "  exterqual extercond foundation bsmtqual bsmtcond bsmtexposure bsmtfintype1  \\\n", "0        Gd        TA      PConc       Gd       TA           No          GLQ   \n", "1        TA        TA     CBlock       Gd       TA           Gd          ALQ   \n", "2        Gd        TA      PConc       Gd       TA           Mn          GLQ   \n", "3        TA        TA     BrkTil       TA       Gd           No          ALQ   \n", "4        Gd        TA      PConc       Gd       TA           Av          GLQ   \n", "\n", "   bsmtfinsf1 bsmtfintype2  bsmtfinsf2  bsmtunfsf  totalbsmtsf heating  \\\n", "0    6.561031          Unf         0.0   5.017280          856    GasA   \n", "1    6.886532          Unf         0.0   5.652489         1262    GasA   \n", "2    6.188264          Unf         0.0   6.075346          920    GasA   \n", "3    5.379897          Unf         0.0   6.293419          756    GasA   \n", "4    6.486161          Unf         0.0   6.196444         1145    GasA   \n", "\n", "  heatingqc centralair electrical  1stflrsf  2ndflrsf  lowqualfinsf  \\\n", "0        Ex          Y      SBrkr       856  6.751101           0.0   \n", "1        Ex          Y      SBrkr      1262  0.000000           0.0   \n", "2        Ex          Y      SBrkr       920  6.765039           0.0   \n", "3        Gd          Y      SBrkr       961  6.629363           0.0   \n", "4        Ex          Y      SBrkr      1145  6.960348           0.0   \n", "\n", "   grl<PERSON><PERSON>  bsm<PERSON><PERSON><PERSON>bath  bsm<PERSON><PERSON>bath  fullbath  halfbath  bedroomabvgr  \\\n", "0   7.444833             1           0.0         2         1             3   \n", "1   7.141245             0           0.0         2         0             3   \n", "2   7.488294             1           0.0         2         1             3   \n", "3   7.448916             1           0.0         1         0             3   \n", "4   7.695758             1           0.0         2         1             4   \n", "\n", "   kitchenabvgr kitchenqual  totrmsabvgrd functional  fireplaces fireplacequ  \\\n", "0      0.693147          Gd             8        Typ           0         NaN   \n", "1      0.693147          TA             6        Typ           1          TA   \n", "2      0.693147          Gd             6        Typ           1          TA   \n", "3      0.693147          Gd             7        Typ           1          Gd   \n", "4      0.693147          Gd             9        Typ           1          TA   \n", "\n", "  garagetype  garageyrblt garagefinish  garagecars  garagearea garagequal  \\\n", "0     Attchd       2003.0          RFn           2         548         TA   \n", "1     Attchd       1976.0          RFn           2         460         TA   \n", "2     Attchd       2001.0          RFn           2         608         TA   \n", "3     Detchd       1998.0          Unf           3         642         TA   \n", "4     Attchd       2000.0          RFn           3         836         TA   \n", "\n", "  garagecond paveddrive  wooddecksf  openporchsf  enclosedporch  3ssnporch  \\\n", "0         TA          Y    0.000000     4.127134            0.0        0.0   \n", "1         TA          Y    5.700444     0.000000            0.0        0.0   \n", "2         TA          Y    0.000000     3.761200            0.0        0.0   \n", "3         TA          Y    0.000000     3.583519            0.0        0.0   \n", "4         TA          Y    5.262690     4.442651            0.0        0.0   \n", "\n", "   screenporch  poolarea poolqc fence  miscval  mosold  yrsold saletype  \\\n", "0          0.0       0.0    NaN   NaN      0.0       2    2008       WD   \n", "1          0.0       0.0    NaN   NaN      0.0       5    2007       WD   \n", "2          0.0       0.0    NaN   NaN      0.0       9    2008       WD   \n", "3          0.0       0.0    NaN   NaN      0.0       2    2006       WD   \n", "4          0.0       0.0    NaN   NaN      0.0      12    2008       WD   \n", "\n", "  salecondition  saleprice   TotalSF  TotalBathrooms  TotalPorchSF  IsNew  \\\n", "0        Normal     208500  7.850493             3.5      4.127134    0.0   \n", "1        Normal     181500  7.833996             2.5      5.700444    0.0   \n", "2        Normal     223500  7.903596             3.5      3.761200    0.0   \n", "3       Abnorml     140000  7.813592             2.0      5.730100    0.0   \n", "4        Normal     250000  8.114923             3.5      5.624018    0.0   \n", "\n", "   HasPool  HasGarage  HasBasement  HasPorch  hasshed  QualxAge  HouseAge_sq  \\\n", "0      0.0   0.693147     0.693147  0.693147      0.0  3.583519     3.258097   \n", "1      0.0   0.693147     0.693147  0.693147      0.0  5.231109     6.869014   \n", "2      0.0   0.693147     0.693147  0.693147      0.0  3.912023     3.912023   \n", "3      0.0   0.693147     0.693147  0.693147      0.0  6.458338     9.021840   \n", "4      0.0   0.693147     0.693147  0.693147      0.0  4.174387     4.174387   \n", "\n", "   NeighborhoodPrice exterior_material  \n", "0          12.195854             Vinyl  \n", "1          12.383272             Metal  \n", "2          12.195854             Vinyl  \n", "3          12.257838              Wood  \n", "4          12.722770             Vinyl  "], "text/html": ["\n", "  <div id=\"df-8cf219ed-c9f7-428c-b661-ca3e1ae03d96\" class=\"colab-df-container\">\n", "    <div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>id</th>\n", "      <th>mssubclass</th>\n", "      <th>mszoning</th>\n", "      <th>lotfrontage</th>\n", "      <th>lotarea</th>\n", "      <th>street</th>\n", "      <th>alley</th>\n", "      <th>lotshape</th>\n", "      <th>landcontour</th>\n", "      <th>utilities</th>\n", "      <th>lotconfig</th>\n", "      <th>landslope</th>\n", "      <th>neighborhood</th>\n", "      <th>condition1</th>\n", "      <th>condition2</th>\n", "      <th>bldgtype</th>\n", "      <th>housestyle</th>\n", "      <th>overallqual</th>\n", "      <th>overallcond</th>\n", "      <th>yearbuilt</th>\n", "      <th>yearremodadd</th>\n", "      <th>roofstyle</th>\n", "      <th>roofmatl</th>\n", "      <th>exterior1st</th>\n", "      <th>masvnrtype</th>\n", "      <th>masvnrarea</th>\n", "      <th>exterqual</th>\n", "      <th>extercond</th>\n", "      <th>foundation</th>\n", "      <th>bsmtqual</th>\n", "      <th>bsmtcond</th>\n", "      <th>bsmtexposure</th>\n", "      <th>bsmtfintype1</th>\n", "      <th>bsmtfinsf1</th>\n", "      <th>bsmtfintype2</th>\n", "      <th>bsmtfinsf2</th>\n", "      <th>bsmtunfsf</th>\n", "      <th>totalbsmtsf</th>\n", "      <th>heating</th>\n", "      <th>heatingqc</th>\n", "      <th>centralair</th>\n", "      <th>electrical</th>\n", "      <th>1stflrsf</th>\n", "      <th>2ndflrsf</th>\n", "      <th>lowqualfinsf</th>\n", "      <th>grlivarea</th>\n", "      <th>bsm<PERSON><PERSON><PERSON><PERSON>h</th>\n", "      <th>bsm<PERSON><PERSON>bath</th>\n", "      <th>fullbath</th>\n", "      <th>halfbath</th>\n", "      <th>bedroomabvgr</th>\n", "      <th>kitchenabvgr</th>\n", "      <th>kitchenqual</th>\n", "      <th>totrmsabvgrd</th>\n", "      <th>functional</th>\n", "      <th>fireplaces</th>\n", "      <th>fireplacequ</th>\n", "      <th>garagetype</th>\n", "      <th>garageyrblt</th>\n", "      <th>garagefinish</th>\n", "      <th>garagecars</th>\n", "      <th>garagearea</th>\n", "      <th>garagequal</th>\n", "      <th>garagecond</th>\n", "      <th>paveddrive</th>\n", "      <th>wooddecksf</th>\n", "      <th>openporchsf</th>\n", "      <th>enclosedporch</th>\n", "      <th>3ssnporch</th>\n", "      <th>screenporch</th>\n", "      <th>poolarea</th>\n", "      <th>poolqc</th>\n", "      <th>fence</th>\n", "      <th>miscval</th>\n", "      <th>mosold</th>\n", "      <th>yrsold</th>\n", "      <th>saletype</th>\n", "      <th>salecondition</th>\n", "      <th>saleprice</th>\n", "      <th>TotalSF</th>\n", "      <th>TotalBathrooms</th>\n", "      <th>TotalPorchSF</th>\n", "      <th>IsNew</th>\n", "      <th>HasPool</th>\n", "      <th>HasGarage</th>\n", "      <th>HasBasement</th>\n", "      <th>HasPorch</th>\n", "      <th>hasshed</th>\n", "      <th>QualxAge</th>\n", "      <th>HouseAge_sq</th>\n", "      <th>NeighborhoodPrice</th>\n", "      <th>exterior_material</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>1</td>\n", "      <td>4.110874</td>\n", "      <td>RL</td>\n", "      <td>65.0</td>\n", "      <td>9.042040</td>\n", "      <td>Pave</td>\n", "      <td>NaN</td>\n", "      <td>Reg</td>\n", "      <td>Lvl</td>\n", "      <td>AllPub</td>\n", "      <td>Inside</td>\n", "      <td>flat</td>\n", "      <td>CollgCr</td>\n", "      <td>Norm</td>\n", "      <td>Norm</td>\n", "      <td>1Fam</td>\n", "      <td>2Story</td>\n", "      <td>7</td>\n", "      <td>5</td>\n", "      <td>2003</td>\n", "      <td>2003</td>\n", "      <td>Gable</td>\n", "      <td>CompShg</td>\n", "      <td>VinylSd</td>\n", "      <td>BrkFace</td>\n", "      <td>5.283204</td>\n", "      <td>Gd</td>\n", "      <td>TA</td>\n", "      <td>PConc</td>\n", "      <td>Gd</td>\n", "      <td>TA</td>\n", "      <td>No</td>\n", "      <td>GLQ</td>\n", "      <td>6.561031</td>\n", "      <td>Unf</td>\n", "      <td>0.0</td>\n", "      <td>5.017280</td>\n", "      <td>856</td>\n", "      <td>GasA</td>\n", "      <td>Ex</td>\n", "      <td>Y</td>\n", "      <td>SBrkr</td>\n", "      <td>856</td>\n", "      <td>6.751101</td>\n", "      <td>0.0</td>\n", "      <td>7.444833</td>\n", "      <td>1</td>\n", "      <td>0.0</td>\n", "      <td>2</td>\n", "      <td>1</td>\n", "      <td>3</td>\n", "      <td>0.693147</td>\n", "      <td>Gd</td>\n", "      <td>8</td>\n", "      <td>Typ</td>\n", "      <td>0</td>\n", "      <td>NaN</td>\n", "      <td>Attchd</td>\n", "      <td>2003.0</td>\n", "      <td>RFn</td>\n", "      <td>2</td>\n", "      <td>548</td>\n", "      <td>TA</td>\n", "      <td>TA</td>\n", "      <td>Y</td>\n", "      <td>0.000000</td>\n", "      <td>4.127134</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>0.0</td>\n", "      <td>2</td>\n", "      <td>2008</td>\n", "      <td>WD</td>\n", "      <td>Normal</td>\n", "      <td>208500</td>\n", "      <td>7.850493</td>\n", "      <td>3.5</td>\n", "      <td>4.127134</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.693147</td>\n", "      <td>0.693147</td>\n", "      <td>0.693147</td>\n", "      <td>0.0</td>\n", "      <td>3.583519</td>\n", "      <td>3.258097</td>\n", "      <td>12.195854</td>\n", "      <td><PERSON><PERSON></td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>2</td>\n", "      <td>3.044522</td>\n", "      <td>RL</td>\n", "      <td>80.0</td>\n", "      <td>9.169623</td>\n", "      <td>Pave</td>\n", "      <td>NaN</td>\n", "      <td>Reg</td>\n", "      <td>Lvl</td>\n", "      <td>AllPub</td>\n", "      <td>FR2</td>\n", "      <td>flat</td>\n", "      <td><PERSON><PERSON><PERSON></td>\n", "      <td><PERSON>edr</td>\n", "      <td>Norm</td>\n", "      <td>1Fam</td>\n", "      <td>1Story</td>\n", "      <td>6</td>\n", "      <td>5</td>\n", "      <td>1976</td>\n", "      <td>1976</td>\n", "      <td>Gable</td>\n", "      <td>CompShg</td>\n", "      <td>MetalSd</td>\n", "      <td>NaN</td>\n", "      <td>0.000000</td>\n", "      <td>TA</td>\n", "      <td>TA</td>\n", "      <td>CBlock</td>\n", "      <td>Gd</td>\n", "      <td>TA</td>\n", "      <td>Gd</td>\n", "      <td>ALQ</td>\n", "      <td>6.886532</td>\n", "      <td>Unf</td>\n", "      <td>0.0</td>\n", "      <td>5.652489</td>\n", "      <td>1262</td>\n", "      <td>GasA</td>\n", "      <td>Ex</td>\n", "      <td>Y</td>\n", "      <td>SBrkr</td>\n", "      <td>1262</td>\n", "      <td>0.000000</td>\n", "      <td>0.0</td>\n", "      <td>7.141245</td>\n", "      <td>0</td>\n", "      <td>0.0</td>\n", "      <td>2</td>\n", "      <td>0</td>\n", "      <td>3</td>\n", "      <td>0.693147</td>\n", "      <td>TA</td>\n", "      <td>6</td>\n", "      <td>Typ</td>\n", "      <td>1</td>\n", "      <td>TA</td>\n", "      <td>Attchd</td>\n", "      <td>1976.0</td>\n", "      <td>RFn</td>\n", "      <td>2</td>\n", "      <td>460</td>\n", "      <td>TA</td>\n", "      <td>TA</td>\n", "      <td>Y</td>\n", "      <td>5.700444</td>\n", "      <td>0.000000</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>0.0</td>\n", "      <td>5</td>\n", "      <td>2007</td>\n", "      <td>WD</td>\n", "      <td>Normal</td>\n", "      <td>181500</td>\n", "      <td>7.833996</td>\n", "      <td>2.5</td>\n", "      <td>5.700444</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.693147</td>\n", "      <td>0.693147</td>\n", "      <td>0.693147</td>\n", "      <td>0.0</td>\n", "      <td>5.231109</td>\n", "      <td>6.869014</td>\n", "      <td>12.383272</td>\n", "      <td>Metal</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>3</td>\n", "      <td>4.110874</td>\n", "      <td>RL</td>\n", "      <td>68.0</td>\n", "      <td>9.328212</td>\n", "      <td>Pave</td>\n", "      <td>NaN</td>\n", "      <td>IR1</td>\n", "      <td>Lvl</td>\n", "      <td>AllPub</td>\n", "      <td>Inside</td>\n", "      <td>flat</td>\n", "      <td>CollgCr</td>\n", "      <td>Norm</td>\n", "      <td>Norm</td>\n", "      <td>1Fam</td>\n", "      <td>2Story</td>\n", "      <td>7</td>\n", "      <td>5</td>\n", "      <td>2001</td>\n", "      <td>2002</td>\n", "      <td>Gable</td>\n", "      <td>CompShg</td>\n", "      <td>VinylSd</td>\n", "      <td>BrkFace</td>\n", "      <td>5.093750</td>\n", "      <td>Gd</td>\n", "      <td>TA</td>\n", "      <td>PConc</td>\n", "      <td>Gd</td>\n", "      <td>TA</td>\n", "      <td>Mn</td>\n", "      <td>GLQ</td>\n", "      <td>6.188264</td>\n", "      <td>Unf</td>\n", "      <td>0.0</td>\n", "      <td>6.075346</td>\n", "      <td>920</td>\n", "      <td>GasA</td>\n", "      <td>Ex</td>\n", "      <td>Y</td>\n", "      <td>SBrkr</td>\n", "      <td>920</td>\n", "      <td>6.765039</td>\n", "      <td>0.0</td>\n", "      <td>7.488294</td>\n", "      <td>1</td>\n", "      <td>0.0</td>\n", "      <td>2</td>\n", "      <td>1</td>\n", "      <td>3</td>\n", "      <td>0.693147</td>\n", "      <td>Gd</td>\n", "      <td>6</td>\n", "      <td>Typ</td>\n", "      <td>1</td>\n", "      <td>TA</td>\n", "      <td>Attchd</td>\n", "      <td>2001.0</td>\n", "      <td>RFn</td>\n", "      <td>2</td>\n", "      <td>608</td>\n", "      <td>TA</td>\n", "      <td>TA</td>\n", "      <td>Y</td>\n", "      <td>0.000000</td>\n", "      <td>3.761200</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>0.0</td>\n", "      <td>9</td>\n", "      <td>2008</td>\n", "      <td>WD</td>\n", "      <td>Normal</td>\n", "      <td>223500</td>\n", "      <td>7.903596</td>\n", "      <td>3.5</td>\n", "      <td>3.761200</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.693147</td>\n", "      <td>0.693147</td>\n", "      <td>0.693147</td>\n", "      <td>0.0</td>\n", "      <td>3.912023</td>\n", "      <td>3.912023</td>\n", "      <td>12.195854</td>\n", "      <td><PERSON><PERSON></td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>4</td>\n", "      <td>4.262680</td>\n", "      <td>RL</td>\n", "      <td>60.0</td>\n", "      <td>9.164401</td>\n", "      <td>Pave</td>\n", "      <td>NaN</td>\n", "      <td>IR1</td>\n", "      <td>Lvl</td>\n", "      <td>AllPub</td>\n", "      <td>Corner</td>\n", "      <td>flat</td>\n", "      <td>Crawfor</td>\n", "      <td>Norm</td>\n", "      <td>Norm</td>\n", "      <td>1Fam</td>\n", "      <td>2Story</td>\n", "      <td>7</td>\n", "      <td>5</td>\n", "      <td>1915</td>\n", "      <td>1970</td>\n", "      <td>Gable</td>\n", "      <td>CompShg</td>\n", "      <td>Wd Sdng</td>\n", "      <td>NaN</td>\n", "      <td>0.000000</td>\n", "      <td>TA</td>\n", "      <td>TA</td>\n", "      <td>BrkTil</td>\n", "      <td>TA</td>\n", "      <td>Gd</td>\n", "      <td>No</td>\n", "      <td>ALQ</td>\n", "      <td>5.379897</td>\n", "      <td>Unf</td>\n", "      <td>0.0</td>\n", "      <td>6.293419</td>\n", "      <td>756</td>\n", "      <td>GasA</td>\n", "      <td>Gd</td>\n", "      <td>Y</td>\n", "      <td>SBrkr</td>\n", "      <td>961</td>\n", "      <td>6.629363</td>\n", "      <td>0.0</td>\n", "      <td>7.448916</td>\n", "      <td>1</td>\n", "      <td>0.0</td>\n", "      <td>1</td>\n", "      <td>0</td>\n", "      <td>3</td>\n", "      <td>0.693147</td>\n", "      <td>Gd</td>\n", "      <td>7</td>\n", "      <td>Typ</td>\n", "      <td>1</td>\n", "      <td>Gd</td>\n", "      <td>Detchd</td>\n", "      <td>1998.0</td>\n", "      <td>Unf</td>\n", "      <td>3</td>\n", "      <td>642</td>\n", "      <td>TA</td>\n", "      <td>TA</td>\n", "      <td>Y</td>\n", "      <td>0.000000</td>\n", "      <td>3.583519</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>0.0</td>\n", "      <td>2</td>\n", "      <td>2006</td>\n", "      <td>WD</td>\n", "      <td>Abnorml</td>\n", "      <td>140000</td>\n", "      <td>7.813592</td>\n", "      <td>2.0</td>\n", "      <td>5.730100</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.693147</td>\n", "      <td>0.693147</td>\n", "      <td>0.693147</td>\n", "      <td>0.0</td>\n", "      <td>6.458338</td>\n", "      <td>9.021840</td>\n", "      <td>12.257838</td>\n", "      <td>Wood</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>5</td>\n", "      <td>4.110874</td>\n", "      <td>RL</td>\n", "      <td>84.0</td>\n", "      <td>9.565284</td>\n", "      <td>Pave</td>\n", "      <td>NaN</td>\n", "      <td>IR1</td>\n", "      <td>Lvl</td>\n", "      <td>AllPub</td>\n", "      <td>FR2</td>\n", "      <td>flat</td>\n", "      <td>NoRidge</td>\n", "      <td>Norm</td>\n", "      <td>Norm</td>\n", "      <td>1Fam</td>\n", "      <td>2Story</td>\n", "      <td>8</td>\n", "      <td>5</td>\n", "      <td>2000</td>\n", "      <td>2000</td>\n", "      <td>Gable</td>\n", "      <td>CompShg</td>\n", "      <td>VinylSd</td>\n", "      <td>BrkFace</td>\n", "      <td>5.860786</td>\n", "      <td>Gd</td>\n", "      <td>TA</td>\n", "      <td>PConc</td>\n", "      <td>Gd</td>\n", "      <td>TA</td>\n", "      <td>Av</td>\n", "      <td>GLQ</td>\n", "      <td>6.486161</td>\n", "      <td>Unf</td>\n", "      <td>0.0</td>\n", "      <td>6.196444</td>\n", "      <td>1145</td>\n", "      <td>GasA</td>\n", "      <td>Ex</td>\n", "      <td>Y</td>\n", "      <td>SBrkr</td>\n", "      <td>1145</td>\n", "      <td>6.960348</td>\n", "      <td>0.0</td>\n", "      <td>7.695758</td>\n", "      <td>1</td>\n", "      <td>0.0</td>\n", "      <td>2</td>\n", "      <td>1</td>\n", "      <td>4</td>\n", "      <td>0.693147</td>\n", "      <td>Gd</td>\n", "      <td>9</td>\n", "      <td>Typ</td>\n", "      <td>1</td>\n", "      <td>TA</td>\n", "      <td>Attchd</td>\n", "      <td>2000.0</td>\n", "      <td>RFn</td>\n", "      <td>3</td>\n", "      <td>836</td>\n", "      <td>TA</td>\n", "      <td>TA</td>\n", "      <td>Y</td>\n", "      <td>5.262690</td>\n", "      <td>4.442651</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>0.0</td>\n", "      <td>12</td>\n", "      <td>2008</td>\n", "      <td>WD</td>\n", "      <td>Normal</td>\n", "      <td>250000</td>\n", "      <td>8.114923</td>\n", "      <td>3.5</td>\n", "      <td>5.624018</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.693147</td>\n", "      <td>0.693147</td>\n", "      <td>0.693147</td>\n", "      <td>0.0</td>\n", "      <td>4.174387</td>\n", "      <td>4.174387</td>\n", "      <td>12.722770</td>\n", "      <td><PERSON><PERSON></td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>\n", "    <div class=\"colab-df-buttons\">\n", "\n", "  <div class=\"colab-df-container\">\n", "    <button class=\"colab-df-convert\" onclick=\"convertToInteractive('df-8cf219ed-c9f7-428c-b661-ca3e1ae03d96')\"\n", "            title=\"Convert this dataframe to an interactive table.\"\n", "            style=\"display:none;\">\n", "\n", "  <svg xmlns=\"http://www.w3.org/2000/svg\" height=\"24px\" viewBox=\"0 -960 960 960\">\n", "    <path d=\"M120-120v-720h720v720H120Zm60-500h600v-160H180v160Zm220 220h160v-160H400v160Zm0 220h160v-160H400v160ZM180-400h160v-160H180v160Zm440 0h160v-160H620v160ZM180-180h160v-160H180v160Zm440 0h160v-160H620v160Z\"/>\n", "  </svg>\n", "    </button>\n", "\n", "  <style>\n", "    .colab-df-container {\n", "      display:flex;\n", "      gap: 12px;\n", "    }\n", "\n", "    .colab-df-convert {\n", "      background-color: #E8F0FE;\n", "      border: none;\n", "      border-radius: 50%;\n", "      cursor: pointer;\n", "      display: none;\n", "      fill: #1967D2;\n", "      height: 32px;\n", "      padding: 0 0 0 0;\n", "      width: 32px;\n", "    }\n", "\n", "    .colab-df-convert:hover {\n", "      background-color: #E2EBFA;\n", "      box-shadow: 0px 1px 2px rgba(60, 64, 67, 0.3), 0px 1px 3px 1px rgba(60, 64, 67, 0.15);\n", "      fill: #174EA6;\n", "    }\n", "\n", "    .colab-df-buttons div {\n", "      margin-bottom: 4px;\n", "    }\n", "\n", "    [theme=dark] .colab-df-convert {\n", "      background-color: #3B4455;\n", "      fill: #D2E3FC;\n", "    }\n", "\n", "    [theme=dark] .colab-df-convert:hover {\n", "      background-color: #434B5C;\n", "      box-shadow: 0px 1px 3px 1px rgba(0, 0, 0, 0.15);\n", "      filter: drop-shadow(0px 1px 2px rgba(0, 0, 0, 0.3));\n", "      fill: #FFFFFF;\n", "    }\n", "  </style>\n", "\n", "    <script>\n", "      const buttonEl =\n", "        document.querySelector('#df-8cf219ed-c9f7-428c-b661-ca3e1ae03d96 button.colab-df-convert');\n", "      buttonEl.style.display =\n", "        google.colab.kernel.accessAllowed ? 'block' : 'none';\n", "\n", "      async function convertToInteractive(key) {\n", "        const element = document.querySelector('#df-8cf219ed-c9f7-428c-b661-ca3e1ae03d96');\n", "        const dataTable =\n", "          await google.colab.kernel.invokeFunction('convertToInteractive',\n", "                                                    [key], {});\n", "        if (!dataTable) return;\n", "\n", "        const docLinkHtml = 'Like what you see? Visit the ' +\n", "          '<a target=\"_blank\" href=https://colab.research.google.com/notebooks/data_table.ipynb>data table notebook</a>'\n", "          + ' to learn more about interactive tables.';\n", "        element.innerHTML = '';\n", "        dataTable['output_type'] = 'display_data';\n", "        await google.colab.output.renderOutput(dataTable, element);\n", "        const docLink = document.createElement('div');\n", "        docLink.innerHTML = docLinkHtml;\n", "        element.appendChild(docLink);\n", "      }\n", "    </script>\n", "  </div>\n", "\n", "\n", "    <div id=\"df-a1193d04-95d0-4376-bd8a-fe6170f69e40\">\n", "      <button class=\"colab-df-quickchart\" onclick=\"quickchart('df-a1193d04-95d0-4376-bd8a-fe6170f69e40')\"\n", "                title=\"Suggest charts\"\n", "                style=\"display:none;\">\n", "\n", "<svg xmlns=\"http://www.w3.org/2000/svg\" height=\"24px\"viewBox=\"0 0 24 24\"\n", "     width=\"24px\">\n", "    <g>\n", "        <path d=\"M19 3H5c-1.1 0-2 .9-2 2v14c0 1.1.9 2 2 2h14c1.1 0 2-.9 2-2V5c0-1.1-.9-2-2-2zM9 17H7v-7h2v7zm4 0h-2V7h2v10zm4 0h-2v-4h2v4z\"/>\n", "    </g>\n", "</svg>\n", "      </button>\n", "\n", "<style>\n", "  .colab-df-quickchart {\n", "      --bg-color: #E8F0FE;\n", "      --fill-color: #1967D2;\n", "      --hover-bg-color: #E2EBFA;\n", "      --hover-fill-color: #174EA6;\n", "      --disabled-fill-color: #AAA;\n", "      --disabled-bg-color: #DDD;\n", "  }\n", "\n", "  [theme=dark] .colab-df-quickchart {\n", "      --bg-color: #3B4455;\n", "      --fill-color: #D2E3FC;\n", "      --hover-bg-color: #434B5C;\n", "      --hover-fill-color: #FFFFFF;\n", "      --disabled-bg-color: #3B4455;\n", "      --disabled-fill-color: #666;\n", "  }\n", "\n", "  .colab-df-quickchart {\n", "    background-color: var(--bg-color);\n", "    border: none;\n", "    border-radius: 50%;\n", "    cursor: pointer;\n", "    display: none;\n", "    fill: var(--fill-color);\n", "    height: 32px;\n", "    padding: 0;\n", "    width: 32px;\n", "  }\n", "\n", "  .colab-df-quickchart:hover {\n", "    background-color: var(--hover-bg-color);\n", "    box-shadow: 0 1px 2px rgba(60, 64, 67, 0.3), 0 1px 3px 1px rgba(60, 64, 67, 0.15);\n", "    fill: var(--button-hover-fill-color);\n", "  }\n", "\n", "  .colab-df-quickchart-complete:disabled,\n", "  .colab-df-quickchart-complete:disabled:hover {\n", "    background-color: var(--disabled-bg-color);\n", "    fill: var(--disabled-fill-color);\n", "    box-shadow: none;\n", "  }\n", "\n", "  .colab-df-spinner {\n", "    border: 2px solid var(--fill-color);\n", "    border-color: transparent;\n", "    border-bottom-color: var(--fill-color);\n", "    animation:\n", "      spin 1s steps(1) infinite;\n", "  }\n", "\n", "  @keyframes spin {\n", "    0% {\n", "      border-color: transparent;\n", "      border-bottom-color: var(--fill-color);\n", "      border-left-color: var(--fill-color);\n", "    }\n", "    20% {\n", "      border-color: transparent;\n", "      border-left-color: var(--fill-color);\n", "      border-top-color: var(--fill-color);\n", "    }\n", "    30% {\n", "      border-color: transparent;\n", "      border-left-color: var(--fill-color);\n", "      border-top-color: var(--fill-color);\n", "      border-right-color: var(--fill-color);\n", "    }\n", "    40% {\n", "      border-color: transparent;\n", "      border-right-color: var(--fill-color);\n", "      border-top-color: var(--fill-color);\n", "    }\n", "    60% {\n", "      border-color: transparent;\n", "      border-right-color: var(--fill-color);\n", "    }\n", "    80% {\n", "      border-color: transparent;\n", "      border-right-color: var(--fill-color);\n", "      border-bottom-color: var(--fill-color);\n", "    }\n", "    90% {\n", "      border-color: transparent;\n", "      border-bottom-color: var(--fill-color);\n", "    }\n", "  }\n", "</style>\n", "\n", "      <script>\n", "        async function quickchart(key) {\n", "          const quickchartButtonEl =\n", "            document.querySelector('#' + key + ' button');\n", "          quickchartButtonEl.disabled = true;  // To prevent multiple clicks.\n", "          quickchartButtonEl.classList.add('colab-df-spinner');\n", "          try {\n", "            const charts = await google.colab.kernel.invokeFunction(\n", "                'suggest<PERSON><PERSON>s', [key], {});\n", "          } catch (error) {\n", "            console.error('Error during call to suggest<PERSON>harts:', error);\n", "          }\n", "          quickchartButtonEl.classList.remove('colab-df-spinner');\n", "          quickchartButtonEl.classList.add('colab-df-quickchart-complete');\n", "        }\n", "        (() => {\n", "          let quickchartButtonEl =\n", "            document.querySelector('#df-a1193d04-95d0-4376-bd8a-fe6170f69e40 button');\n", "          quickchartButtonEl.style.display =\n", "            google.colab.kernel.accessAllowed ? 'block' : 'none';\n", "        })();\n", "      </script>\n", "    </div>\n", "\n", "    </div>\n", "  </div>\n"], "application/vnd.google.colaboratory.intrinsic+json": {"type": "dataframe"}}, "metadata": {}}]}, {"cell_type": "markdown", "source": ["# Split Data into Training and Testing Sets\n", "\n", "\n", "\n", "**Purpose:** Before training any machine learning model, it is crucial to split our dataset into two separate sets: a **training set** and a **testing set**.\n", "\n", "\n", "\n", "*   **Training Set:** This subset of the data (typically 70-80%) is used to teach the model. The model learns the patterns, relationships, and rules from this data.\n", "\n", "*   **Testing Set:** This subset (the remaining 20-30%) is held back and *not* shown to the model during training. It acts as unseen data to evaluate how well the model generalizes to new, previously unobserved examples. This helps us estimate the model's real-world performance and avoid overfitting (where the model learns the training data too well but fails on new data).\n", "\n", "\n", "\n", "We first separate our features (X) from the target variable (y, which is `saleprice` in our case). Then, we use scikit-learn's `train_test_split` function to perform the random split.\n"], "metadata": {"id": "dR-CYwrt5bPL"}}, {"cell_type": "code", "source": ["from sklearn.model_selection import train_test_split # Import the function for splitting data\n", "\n", "\n", "\n", "# Ensure the reloaded dataframe 'df' (containing fully preprocessed data) is used\n", "\n", "# Separate features (X) and the target variable (y)\n", "\n", "if \"saleprice\" in df.columns:\n", "\n", "    # X contains all columns EXCEPT the target variable 'saleprice'\n", "\n", "    X = df.drop(\"saleprice\", axis=1)\n", "\n", "\n", "\n", "    # y contains ONLY the target variable 'saleprice' (which is already log-transformed)\n", "\n", "    y = df[\"saleprice\"]\n", "\n", "\n", "\n", "    print(f\"Features (X) shape: {X.shape} - {X.shape[0]} rows, {X.shape[1]} features\")\n", "\n", "    print(f\"Target (y) shape: {y.shape} - {y.shape[0]} values\")\n", "\n", "else:\n", "\n", "    print(\"Error: 'saleprice' column not found in the reloaded dataframe.\")\n", "\n", "    # Handle error appropriately, maybe exit()\n", "\n", "    X, y = None, None # Set to None to prevent further errors\n", "\n", "\n", "\n", "if X is not None and y is not None:\n", "\n", "\n", "    X_train, X_test, y_train, y_test = train_test_split(X, y, test_size=0.2, random_state=42)\n", "\n", "\n", "\n", "    print(\"\\nData successfully split into training and testing sets:\")\n", "\n", "    print(f\"  X_train shape: {X_train.shape} - Training features\")\n", "\n", "    print(f\"  X_test shape: {X_test.shape} - Testing features\")\n", "\n", "    print(f\"  y_train shape: {y_train.shape} - Training target\")\n", "\n", "    print(f\"  y_test shape: {y_test.shape} - Testing target\")\n", "\n", "else:\n", "\n", "    print(\"\\nSkipping train/test split due to missing target variable.\")\n"], "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "M25jDdKpzo5y", "outputId": "5d48cf4b-0d24-4483-9526-8ee9fe8dc824"}, "execution_count": 117, "outputs": [{"output_type": "stream", "name": "stdout", "text": ["Features (X) shape: (1197, 91) - 1197 rows, 91 features\n", "Target (y) shape: (1197,) - 1197 values\n", "\n", "Data successfully split into training and testing sets:\n", "  X_train shape: (957, 91) - Training features\n", "  X_test shape: (240, 91) - Testing features\n", "  y_train shape: (957,) - Training target\n", "  y_test shape: (240,) - Testing target\n"]}]}, {"cell_type": "code", "source": [], "metadata": {"id": "-1TViHKwzpEU"}, "execution_count": 110, "outputs": []}, {"cell_type": "code", "source": [], "metadata": {"id": "0wKMRbN1yZwA"}, "execution_count": 110, "outputs": []}, {"cell_type": "code", "source": [], "metadata": {"id": "e31CkceGyZ8e"}, "execution_count": 110, "outputs": []}, {"cell_type": "code", "source": [], "metadata": {"id": "t8QYGffh-9H6"}, "execution_count": 110, "outputs": []}, {"cell_type": "code", "source": [], "metadata": {"id": "0GG4T-Ec-9bY"}, "execution_count": 110, "outputs": []}]}