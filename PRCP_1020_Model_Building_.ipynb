{"nbformat": 4, "nbformat_minor": 0, "metadata": {"colab": {"provenance": []}, "kernelspec": {"name": "python3", "display_name": "Python 3"}, "language_info": {"name": "python"}}, "cells": [{"cell_type": "markdown", "source": ["\n", "\n", "# PRCP-1020-HousePricePred - Data Processing & Model Building\n", "\n", "# Problem Statement\n", "\n", "Task 1:- Prepare a complete data analysis report on the given data.\n", "\n", "Task 2:-a) Create a robust machine learning algorithm to accurately predict the price of the house given the various factors across the market.      \n", "            b) Determine the relationship between the house features and how the price varies based on this.\n", "\n", "Task3:- Come up with suggestions for the customer to buy the house according to the area, price and other requirements.\n", "\n", "\n", "# Dataset Description and Link:\n", "Ask a home buyer to describe their dream house, and they probably won't begin with the height of the basement ceiling or the proximity to an east-west railroad. But this playground competition's dataset proves that much more influences price negotiations than the number of bedrooms or a white-picket fence.\n", "With 79 explanatory variables describing (almost) every aspect of residential homes in Ames, Iowa, this competition challenges you to predict the final price of each home.\n", "# Practice Skills\n", "●\tCreative feature engineering.\n", "\n", "●\tAdvanced regression techniques like random forest and gradient boosting.\n", "\n", "\n", "Link : https://d3ilbtxij3aepc.cloudfront.net/projects/CDS-Capstone-Projects/PRCP-1020-HousePricePred.zip\n", "\n", "\n", "\n"], "metadata": {"id": "0L78JeW-5jWD"}}, {"cell_type": "markdown", "source": ["# 1. Imports Libraries"], "metadata": {"id": "1cC5oeQ30Qei"}}, {"cell_type": "code", "execution_count": 12, "metadata": {"id": "YGv5hgCJzhyJ"}, "outputs": [], "source": ["import pandas as pd\n", "import numpy as np\n", "import matplotlib.pyplot as plt\n", "from sklearn.impute import SimpleImputer\n", "from sklearn.model_selection  import train_test_split\n", "from sklearn.preprocessing   import StandardScaler\n", "from sklearn.dummy           import DummyRegressor\n", "from sklearn.linear_model    import (\n", "    LinearRegression,\n", "    LassoCV, RidgeCV, ElasticNetCV\n", ")\n", "from sklearn.metrics import mean_squared_error, r2_score\n", "from sklearn.metrics import r2_score, mean_squared_error"]}, {"cell_type": "markdown", "source": ["# 2. Load Preprocessed Data\n", "\n", "* We assume that all data cleaning and feature engineering steps (handling missing values, outlier treatment, feature grouping) have been completed, and the resulting CSV contains numeric and categorical columns ready for modeling."], "metadata": {"id": "pdDazx9d1Ajv"}}, {"cell_type": "code", "source": ["# Adjust the path if needed\n", "df = pd.read_csv(\"/content/house_price_preprocessed_data_final.csv\")\n", "print(\"Raw data shape:\", df.shape)\n", "df.head()"], "metadata": {"colab": {"base_uri": "https://localhost:8080/", "height": 253}, "id": "FYNzLeqlzv7p", "outputId": "3145bd45-92e0-477c-db71-2897f70c4507"}, "execution_count": 13, "outputs": [{"output_type": "stream", "name": "stdout", "text": ["Raw data shape: (1197, 92)\n"]}, {"output_type": "execute_result", "data": {"text/plain": ["   id  mssubclass mszoning  lotfrontage   lotarea street alley lotshape  \\\n", "0   1    4.110874       RL         65.0  9.042040   Pave   NaN      Reg   \n", "1   2    3.044522       RL         80.0  9.169623   Pave   NaN      Reg   \n", "2   3    4.110874       RL         68.0  9.328212   Pave   NaN      IR1   \n", "3   4    4.262680       RL         60.0  9.164401   Pave   NaN      IR1   \n", "4   5    4.110874       RL         84.0  9.565284   Pave   NaN      IR1   \n", "\n", "  landcontour utilities  ... IsNew HasPool HasGarage HasBasement  HasPorch  \\\n", "0         Lvl    AllPub  ...   0.0     0.0  0.693147    0.693147  0.693147   \n", "1         Lvl    AllPub  ...   0.0     0.0  0.693147    0.693147  0.693147   \n", "2         Lvl    AllPub  ...   0.0     0.0  0.693147    0.693147  0.693147   \n", "3         Lvl    AllPub  ...   0.0     0.0  0.693147    0.693147  0.693147   \n", "4         Lvl    AllPub  ...   0.0     0.0  0.693147    0.693147  0.693147   \n", "\n", "  hasshed  QualxAge  HouseAge_sq  NeighborhoodPrice  exterior_material  \n", "0     0.0  3.583519     3.258097          12.195854              Vinyl  \n", "1     0.0  5.231109     6.869014          12.383272              Metal  \n", "2     0.0  3.912023     3.912023          12.195854              Vinyl  \n", "3     0.0  6.458338     9.021840          12.257838               Wood  \n", "4     0.0  4.174387     4.174387          12.722770              Vinyl  \n", "\n", "[5 rows x 92 columns]"], "text/html": ["\n", "  <div id=\"df-9e30a31e-0da7-44f5-a10e-f15c2556312a\" class=\"colab-df-container\">\n", "    <div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>id</th>\n", "      <th>mssubclass</th>\n", "      <th>mszoning</th>\n", "      <th>lotfrontage</th>\n", "      <th>lotarea</th>\n", "      <th>street</th>\n", "      <th>alley</th>\n", "      <th>lotshape</th>\n", "      <th>landcontour</th>\n", "      <th>utilities</th>\n", "      <th>...</th>\n", "      <th>IsNew</th>\n", "      <th>HasPool</th>\n", "      <th>HasGarage</th>\n", "      <th>HasBasement</th>\n", "      <th>HasPorch</th>\n", "      <th>hasshed</th>\n", "      <th>QualxAge</th>\n", "      <th>HouseAge_sq</th>\n", "      <th>NeighborhoodPrice</th>\n", "      <th>exterior_material</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>1</td>\n", "      <td>4.110874</td>\n", "      <td>RL</td>\n", "      <td>65.0</td>\n", "      <td>9.042040</td>\n", "      <td>Pave</td>\n", "      <td>NaN</td>\n", "      <td>Reg</td>\n", "      <td>Lvl</td>\n", "      <td>AllPub</td>\n", "      <td>...</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.693147</td>\n", "      <td>0.693147</td>\n", "      <td>0.693147</td>\n", "      <td>0.0</td>\n", "      <td>3.583519</td>\n", "      <td>3.258097</td>\n", "      <td>12.195854</td>\n", "      <td><PERSON><PERSON></td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>2</td>\n", "      <td>3.044522</td>\n", "      <td>RL</td>\n", "      <td>80.0</td>\n", "      <td>9.169623</td>\n", "      <td>Pave</td>\n", "      <td>NaN</td>\n", "      <td>Reg</td>\n", "      <td>Lvl</td>\n", "      <td>AllPub</td>\n", "      <td>...</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.693147</td>\n", "      <td>0.693147</td>\n", "      <td>0.693147</td>\n", "      <td>0.0</td>\n", "      <td>5.231109</td>\n", "      <td>6.869014</td>\n", "      <td>12.383272</td>\n", "      <td>Metal</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>3</td>\n", "      <td>4.110874</td>\n", "      <td>RL</td>\n", "      <td>68.0</td>\n", "      <td>9.328212</td>\n", "      <td>Pave</td>\n", "      <td>NaN</td>\n", "      <td>IR1</td>\n", "      <td>Lvl</td>\n", "      <td>AllPub</td>\n", "      <td>...</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.693147</td>\n", "      <td>0.693147</td>\n", "      <td>0.693147</td>\n", "      <td>0.0</td>\n", "      <td>3.912023</td>\n", "      <td>3.912023</td>\n", "      <td>12.195854</td>\n", "      <td><PERSON><PERSON></td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>4</td>\n", "      <td>4.262680</td>\n", "      <td>RL</td>\n", "      <td>60.0</td>\n", "      <td>9.164401</td>\n", "      <td>Pave</td>\n", "      <td>NaN</td>\n", "      <td>IR1</td>\n", "      <td>Lvl</td>\n", "      <td>AllPub</td>\n", "      <td>...</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.693147</td>\n", "      <td>0.693147</td>\n", "      <td>0.693147</td>\n", "      <td>0.0</td>\n", "      <td>6.458338</td>\n", "      <td>9.021840</td>\n", "      <td>12.257838</td>\n", "      <td>Wood</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>5</td>\n", "      <td>4.110874</td>\n", "      <td>RL</td>\n", "      <td>84.0</td>\n", "      <td>9.565284</td>\n", "      <td>Pave</td>\n", "      <td>NaN</td>\n", "      <td>IR1</td>\n", "      <td>Lvl</td>\n", "      <td>AllPub</td>\n", "      <td>...</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.693147</td>\n", "      <td>0.693147</td>\n", "      <td>0.693147</td>\n", "      <td>0.0</td>\n", "      <td>4.174387</td>\n", "      <td>4.174387</td>\n", "      <td>12.722770</td>\n", "      <td><PERSON><PERSON></td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "<p>5 rows × 92 columns</p>\n", "</div>\n", "    <div class=\"colab-df-buttons\">\n", "\n", "  <div class=\"colab-df-container\">\n", "    <button class=\"colab-df-convert\" onclick=\"convertToInteractive('df-9e30a31e-0da7-44f5-a10e-f15c2556312a')\"\n", "            title=\"Convert this dataframe to an interactive table.\"\n", "            style=\"display:none;\">\n", "\n", "  <svg xmlns=\"http://www.w3.org/2000/svg\" height=\"24px\" viewBox=\"0 -960 960 960\">\n", "    <path d=\"M120-120v-720h720v720H120Zm60-500h600v-160H180v160Zm220 220h160v-160H400v160Zm0 220h160v-160H400v160ZM180-400h160v-160H180v160Zm440 0h160v-160H620v160ZM180-180h160v-160H180v160Zm440 0h160v-160H620v160Z\"/>\n", "  </svg>\n", "    </button>\n", "\n", "  <style>\n", "    .colab-df-container {\n", "      display:flex;\n", "      gap: 12px;\n", "    }\n", "\n", "    .colab-df-convert {\n", "      background-color: #E8F0FE;\n", "      border: none;\n", "      border-radius: 50%;\n", "      cursor: pointer;\n", "      display: none;\n", "      fill: #1967D2;\n", "      height: 32px;\n", "      padding: 0 0 0 0;\n", "      width: 32px;\n", "    }\n", "\n", "    .colab-df-convert:hover {\n", "      background-color: #E2EBFA;\n", "      box-shadow: 0px 1px 2px rgba(60, 64, 67, 0.3), 0px 1px 3px 1px rgba(60, 64, 67, 0.15);\n", "      fill: #174EA6;\n", "    }\n", "\n", "    .colab-df-buttons div {\n", "      margin-bottom: 4px;\n", "    }\n", "\n", "    [theme=dark] .colab-df-convert {\n", "      background-color: #3B4455;\n", "      fill: #D2E3FC;\n", "    }\n", "\n", "    [theme=dark] .colab-df-convert:hover {\n", "      background-color: #434B5C;\n", "      box-shadow: 0px 1px 3px 1px rgba(0, 0, 0, 0.15);\n", "      filter: drop-shadow(0px 1px 2px rgba(0, 0, 0, 0.3));\n", "      fill: #FFFFFF;\n", "    }\n", "  </style>\n", "\n", "    <script>\n", "      const buttonEl =\n", "        document.querySelector('#df-9e30a31e-0da7-44f5-a10e-f15c2556312a button.colab-df-convert');\n", "      buttonEl.style.display =\n", "        google.colab.kernel.accessAllowed ? 'block' : 'none';\n", "\n", "      async function convertToInteractive(key) {\n", "        const element = document.querySelector('#df-9e30a31e-0da7-44f5-a10e-f15c2556312a');\n", "        const dataTable =\n", "          await google.colab.kernel.invokeFunction('convertToInteractive',\n", "                                                    [key], {});\n", "        if (!dataTable) return;\n", "\n", "        const docLinkHtml = 'Like what you see? Visit the ' +\n", "          '<a target=\"_blank\" href=https://colab.research.google.com/notebooks/data_table.ipynb>data table notebook</a>'\n", "          + ' to learn more about interactive tables.';\n", "        element.innerHTML = '';\n", "        dataTable['output_type'] = 'display_data';\n", "        await google.colab.output.renderOutput(dataTable, element);\n", "        const docLink = document.createElement('div');\n", "        docLink.innerHTML = docLinkHtml;\n", "        element.appendChild(docLink);\n", "      }\n", "    </script>\n", "  </div>\n", "\n", "\n", "    <div id=\"df-4e129495-00af-4b42-8cc0-27481854cca7\">\n", "      <button class=\"colab-df-quickchart\" onclick=\"quickchart('df-4e129495-00af-4b42-8cc0-27481854cca7')\"\n", "                title=\"Suggest charts\"\n", "                style=\"display:none;\">\n", "\n", "<svg xmlns=\"http://www.w3.org/2000/svg\" height=\"24px\"viewBox=\"0 0 24 24\"\n", "     width=\"24px\">\n", "    <g>\n", "        <path d=\"M19 3H5c-1.1 0-2 .9-2 2v14c0 1.1.9 2 2 2h14c1.1 0 2-.9 2-2V5c0-1.1-.9-2-2-2zM9 17H7v-7h2v7zm4 0h-2V7h2v10zm4 0h-2v-4h2v4z\"/>\n", "    </g>\n", "</svg>\n", "      </button>\n", "\n", "<style>\n", "  .colab-df-quickchart {\n", "      --bg-color: #E8F0FE;\n", "      --fill-color: #1967D2;\n", "      --hover-bg-color: #E2EBFA;\n", "      --hover-fill-color: #174EA6;\n", "      --disabled-fill-color: #AAA;\n", "      --disabled-bg-color: #DDD;\n", "  }\n", "\n", "  [theme=dark] .colab-df-quickchart {\n", "      --bg-color: #3B4455;\n", "      --fill-color: #D2E3FC;\n", "      --hover-bg-color: #434B5C;\n", "      --hover-fill-color: #FFFFFF;\n", "      --disabled-bg-color: #3B4455;\n", "      --disabled-fill-color: #666;\n", "  }\n", "\n", "  .colab-df-quickchart {\n", "    background-color: var(--bg-color);\n", "    border: none;\n", "    border-radius: 50%;\n", "    cursor: pointer;\n", "    display: none;\n", "    fill: var(--fill-color);\n", "    height: 32px;\n", "    padding: 0;\n", "    width: 32px;\n", "  }\n", "\n", "  .colab-df-quickchart:hover {\n", "    background-color: var(--hover-bg-color);\n", "    box-shadow: 0 1px 2px rgba(60, 64, 67, 0.3), 0 1px 3px 1px rgba(60, 64, 67, 0.15);\n", "    fill: var(--button-hover-fill-color);\n", "  }\n", "\n", "  .colab-df-quickchart-complete:disabled,\n", "  .colab-df-quickchart-complete:disabled:hover {\n", "    background-color: var(--disabled-bg-color);\n", "    fill: var(--disabled-fill-color);\n", "    box-shadow: none;\n", "  }\n", "\n", "  .colab-df-spinner {\n", "    border: 2px solid var(--fill-color);\n", "    border-color: transparent;\n", "    border-bottom-color: var(--fill-color);\n", "    animation:\n", "      spin 1s steps(1) infinite;\n", "  }\n", "\n", "  @keyframes spin {\n", "    0% {\n", "      border-color: transparent;\n", "      border-bottom-color: var(--fill-color);\n", "      border-left-color: var(--fill-color);\n", "    }\n", "    20% {\n", "      border-color: transparent;\n", "      border-left-color: var(--fill-color);\n", "      border-top-color: var(--fill-color);\n", "    }\n", "    30% {\n", "      border-color: transparent;\n", "      border-left-color: var(--fill-color);\n", "      border-top-color: var(--fill-color);\n", "      border-right-color: var(--fill-color);\n", "    }\n", "    40% {\n", "      border-color: transparent;\n", "      border-right-color: var(--fill-color);\n", "      border-top-color: var(--fill-color);\n", "    }\n", "    60% {\n", "      border-color: transparent;\n", "      border-right-color: var(--fill-color);\n", "    }\n", "    80% {\n", "      border-color: transparent;\n", "      border-right-color: var(--fill-color);\n", "      border-bottom-color: var(--fill-color);\n", "    }\n", "    90% {\n", "      border-color: transparent;\n", "      border-bottom-color: var(--fill-color);\n", "    }\n", "  }\n", "</style>\n", "\n", "      <script>\n", "        async function quickchart(key) {\n", "          const quickchartButtonEl =\n", "            document.querySelector('#' + key + ' button');\n", "          quickchartButtonEl.disabled = true;  // To prevent multiple clicks.\n", "          quickchartButtonEl.classList.add('colab-df-spinner');\n", "          try {\n", "            const charts = await google.colab.kernel.invokeFunction(\n", "                'suggest<PERSON><PERSON>s', [key], {});\n", "          } catch (error) {\n", "            console.error('Error during call to suggest<PERSON>harts:', error);\n", "          }\n", "          quickchartButtonEl.classList.remove('colab-df-spinner');\n", "          quickchartButtonEl.classList.add('colab-df-quickchart-complete');\n", "        }\n", "        (() => {\n", "          let quickchartButtonEl =\n", "            document.querySelector('#df-4e129495-00af-4b42-8cc0-27481854cca7 button');\n", "          quickchartButtonEl.style.display =\n", "            google.colab.kernel.accessAllowed ? 'block' : 'none';\n", "        })();\n", "      </script>\n", "    </div>\n", "\n", "    </div>\n", "  </div>\n"], "application/vnd.google.colaboratory.intrinsic+json": {"type": "dataframe", "variable_name": "df"}}, "metadata": {}, "execution_count": 13}]}, {"cell_type": "code", "source": ["df.info()"], "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "bgYKKbJ6EoS_", "outputId": "4d9f6dfb-b4bc-4f95-9cc0-ef8a11c52a67"}, "execution_count": 14, "outputs": [{"output_type": "stream", "name": "stdout", "text": ["<class 'pandas.core.frame.DataFrame'>\n", "RangeIndex: 1197 entries, 0 to 1196\n", "Data columns (total 92 columns):\n", " #   Column             Non-Null Count  Dtype  \n", "---  ------             --------------  -----  \n", " 0   id                 1197 non-null   int64  \n", " 1   mssubclass         1197 non-null   float64\n", " 2   mszoning           1197 non-null   object \n", " 3   lotfrontage        1197 non-null   float64\n", " 4   lotarea            1197 non-null   float64\n", " 5   street             1197 non-null   object \n", " 6   alley              86 non-null     object \n", " 7   lotshape           1197 non-null   object \n", " 8   landcontour        1197 non-null   object \n", " 9   utilities          1197 non-null   object \n", " 10  lotconfig          1197 non-null   object \n", " 11  landslope          1197 non-null   object \n", " 12  neighborhood       1197 non-null   object \n", " 13  condition1         1197 non-null   object \n", " 14  condition2         1197 non-null   object \n", " 15  bldgtype           1197 non-null   object \n", " 16  housestyle         1197 non-null   object \n", " 17  overallqual        1197 non-null   int64  \n", " 18  overallcond        1197 non-null   int64  \n", " 19  yearbuilt          1197 non-null   int64  \n", " 20  yearremodadd       1197 non-null   int64  \n", " 21  roofstyle          1197 non-null   object \n", " 22  roofmatl           1197 non-null   object \n", " 23  exterior1st        1197 non-null   object \n", " 24  masvnrtype         465 non-null    object \n", " 25  masvnrarea         1191 non-null   float64\n", " 26  exterqual          1197 non-null   object \n", " 27  extercond          1197 non-null   object \n", " 28  foundation         1197 non-null   object \n", " 29  bsmtqual           1166 non-null   object \n", " 30  bsmtcond           1166 non-null   object \n", " 31  bsmtexposure       1165 non-null   object \n", " 32  bsmtfintype1       1166 non-null   object \n", " 33  bsmtfinsf1         1197 non-null   float64\n", " 34  bsmtfintype2       1165 non-null   object \n", " 35  bsmtfinsf2         1197 non-null   float64\n", " 36  bsmtunfsf          1197 non-null   float64\n", " 37  totalbsmtsf        1197 non-null   int64  \n", " 38  heating            1197 non-null   object \n", " 39  heatingqc          1197 non-null   object \n", " 40  centralair         1197 non-null   object \n", " 41  electrical         1196 non-null   object \n", " 42  1stflrsf           1197 non-null   int64  \n", " 43  2ndflrsf           1197 non-null   float64\n", " 44  lowqualfinsf       1197 non-null   float64\n", " 45  grlivarea          1197 non-null   float64\n", " 46  bsm<PERSON><PERSON><PERSON><PERSON>h       1197 non-null   int64  \n", " 47  bsmthalfbath       1197 non-null   float64\n", " 48  fullbath           1197 non-null   int64  \n", " 49  halfbath           1197 non-null   int64  \n", " 50  bedroomabvgr       1197 non-null   int64  \n", " 51  kitchenabvgr       1197 non-null   float64\n", " 52  kitchenqual        1197 non-null   object \n", " 53  totrmsabvgrd       1197 non-null   int64  \n", " 54  functional         1197 non-null   object \n", " 55  fireplaces         1197 non-null   int64  \n", " 56  fireplacequ        597 non-null    object \n", " 57  garagetype         1123 non-null   object \n", " 58  garageyrblt        1123 non-null   float64\n", " 59  garagefinish       1123 non-null   object \n", " 60  garagecars         1197 non-null   int64  \n", " 61  garagearea         1197 non-null   int64  \n", " 62  garagequal         1123 non-null   object \n", " 63  garagecond         1123 non-null   object \n", " 64  paveddrive         1197 non-null   object \n", " 65  wooddecksf         1197 non-null   float64\n", " 66  openporchsf        1197 non-null   float64\n", " 67  enclosedporch      1197 non-null   float64\n", " 68  3ssnporch          1197 non-null   float64\n", " 69  screenporch        1197 non-null   float64\n", " 70  poolarea           1197 non-null   float64\n", " 71  poolqc             5 non-null      object \n", " 72  fence              228 non-null    object \n", " 73  miscval            1197 non-null   float64\n", " 74  mosold             1197 non-null   int64  \n", " 75  yrsold             1197 non-null   int64  \n", " 76  saletype           1197 non-null   object \n", " 77  salecondition      1197 non-null   object \n", " 78  saleprice          1197 non-null   int64  \n", " 79  TotalSF            1197 non-null   float64\n", " 80  TotalBathrooms     1197 non-null   float64\n", " 81  TotalPorchSF       1197 non-null   float64\n", " 82  IsNew              1197 non-null   float64\n", " 83  HasPool            1197 non-null   float64\n", " 84  HasGarage          1197 non-null   float64\n", " 85  HasBasement        1197 non-null   float64\n", " 86  HasPorch           1197 non-null   float64\n", " 87  hasshed            1197 non-null   float64\n", " 88  QualxAge           1197 non-null   float64\n", " 89  HouseAge_sq        1197 non-null   float64\n", " 90  NeighborhoodPrice  1197 non-null   float64\n", " 91  exterior_material  1197 non-null   object \n", "dtypes: float64(32), int64(18), object(42)\n", "memory usage: 860.5+ KB\n"]}]}, {"cell_type": "code", "source": ["# convert mssubclass to string again since it got converted back to int on import\n", "df['mssubclass'] = df['mssubclass'].astype(str)"], "metadata": {"id": "B0pLEOPJFmB4"}, "execution_count": 15, "outputs": []}, {"cell_type": "markdown", "source": ["#3. One-Hot Encode Categorical Variables\n", "\n", " * Convert categorical features to dummy variables, dropping the first level to avoid multicollinearity."], "metadata": {"id": "UQII7aTt1F8S"}}, {"cell_type": "code", "source": ["# We assume categorical columns remain; one-hot encode them\n", "df_encoded = pd.get_dummies(df, drop_first=True)\n", "print(\"After one-hot encoding:\", df_encoded.shape)\n", "df_encoded.head()"], "metadata": {"colab": {"base_uri": "https://localhost:8080/", "height": 273}, "id": "o63_hrd1zwBi", "outputId": "ff7d652b-f9b0-45cf-80f4-7ef4796c0381"}, "execution_count": 16, "outputs": [{"output_type": "stream", "name": "stdout", "text": ["After one-hot encoding: (1197, 253)\n"]}, {"output_type": "execute_result", "data": {"text/plain": ["   id  lotfrontage   lotarea  overallqual  overallcond  yearbuilt  \\\n", "0   1         65.0  9.042040            7            5       2003   \n", "1   2         80.0  9.169623            6            5       1976   \n", "2   3         68.0  9.328212            7            5       2001   \n", "3   4         60.0  9.164401            7            5       1915   \n", "4   5         84.0  9.565284            8            5       2000   \n", "\n", "   yearremodadd  masvnrarea  bsmtfinsf1  bsmtfinsf2  ...  \\\n", "0          2003    5.283204    6.561031         0.0  ...   \n", "1          1976    0.000000    6.886532         0.0  ...   \n", "2          2002    5.093750    6.188264         0.0  ...   \n", "3          1970    0.000000    5.379897         0.0  ...   \n", "4          2000    5.860786    6.486161         0.0  ...   \n", "\n", "   salecondition_Partial  exterior_material_Asphalt  exterior_material_Brick  \\\n", "0                  False                      False                    False   \n", "1                  False                      False                    False   \n", "2                  False                      False                    False   \n", "3                  False                      False                    False   \n", "4                  False                      False                    False   \n", "\n", "   exterior_material_CBlock  exterior_material_Cement  \\\n", "0                     False                     False   \n", "1                     False                     False   \n", "2                     False                     False   \n", "3                     False                     False   \n", "4                     False                     False   \n", "\n", "   exterior_material_Metal  exterior_material_Stone  exterior_material_Stucco  \\\n", "0                    False                    False                     False   \n", "1                     True                    False                     False   \n", "2                    False                    False                     False   \n", "3                    False                    False                     False   \n", "4                    False                    False                     False   \n", "\n", "   exterior_material_Vinyl  exterior_material_Wood  \n", "0                     True                   False  \n", "1                    False                   False  \n", "2                     True                   False  \n", "3                    False                    True  \n", "4                     True                   False  \n", "\n", "[5 rows x 253 columns]"], "text/html": ["\n", "  <div id=\"df-dcc94098-ae18-439a-ac3e-137fb89376de\" class=\"colab-df-container\">\n", "    <div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>id</th>\n", "      <th>lotfrontage</th>\n", "      <th>lotarea</th>\n", "      <th>overallqual</th>\n", "      <th>overallcond</th>\n", "      <th>yearbuilt</th>\n", "      <th>yearremodadd</th>\n", "      <th>masvnrarea</th>\n", "      <th>bsmtfinsf1</th>\n", "      <th>bsmtfinsf2</th>\n", "      <th>...</th>\n", "      <th>salecondition_Partial</th>\n", "      <th>exterior_material_Asphalt</th>\n", "      <th>exterior_material_Brick</th>\n", "      <th>exterior_material_CBlock</th>\n", "      <th>exterior_material_Cement</th>\n", "      <th>exterior_material_Metal</th>\n", "      <th>exterior_material_Stone</th>\n", "      <th>exterior_material_Stucco</th>\n", "      <th>exterior_material_Vinyl</th>\n", "      <th>exterior_material_Wood</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>1</td>\n", "      <td>65.0</td>\n", "      <td>9.042040</td>\n", "      <td>7</td>\n", "      <td>5</td>\n", "      <td>2003</td>\n", "      <td>2003</td>\n", "      <td>5.283204</td>\n", "      <td>6.561031</td>\n", "      <td>0.0</td>\n", "      <td>...</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>True</td>\n", "      <td>False</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>2</td>\n", "      <td>80.0</td>\n", "      <td>9.169623</td>\n", "      <td>6</td>\n", "      <td>5</td>\n", "      <td>1976</td>\n", "      <td>1976</td>\n", "      <td>0.000000</td>\n", "      <td>6.886532</td>\n", "      <td>0.0</td>\n", "      <td>...</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>True</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>3</td>\n", "      <td>68.0</td>\n", "      <td>9.328212</td>\n", "      <td>7</td>\n", "      <td>5</td>\n", "      <td>2001</td>\n", "      <td>2002</td>\n", "      <td>5.093750</td>\n", "      <td>6.188264</td>\n", "      <td>0.0</td>\n", "      <td>...</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>True</td>\n", "      <td>False</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>4</td>\n", "      <td>60.0</td>\n", "      <td>9.164401</td>\n", "      <td>7</td>\n", "      <td>5</td>\n", "      <td>1915</td>\n", "      <td>1970</td>\n", "      <td>0.000000</td>\n", "      <td>5.379897</td>\n", "      <td>0.0</td>\n", "      <td>...</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>True</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>5</td>\n", "      <td>84.0</td>\n", "      <td>9.565284</td>\n", "      <td>8</td>\n", "      <td>5</td>\n", "      <td>2000</td>\n", "      <td>2000</td>\n", "      <td>5.860786</td>\n", "      <td>6.486161</td>\n", "      <td>0.0</td>\n", "      <td>...</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>True</td>\n", "      <td>False</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "<p>5 rows × 253 columns</p>\n", "</div>\n", "    <div class=\"colab-df-buttons\">\n", "\n", "  <div class=\"colab-df-container\">\n", "    <button class=\"colab-df-convert\" onclick=\"convertToInteractive('df-dcc94098-ae18-439a-ac3e-137fb89376de')\"\n", "            title=\"Convert this dataframe to an interactive table.\"\n", "            style=\"display:none;\">\n", "\n", "  <svg xmlns=\"http://www.w3.org/2000/svg\" height=\"24px\" viewBox=\"0 -960 960 960\">\n", "    <path d=\"M120-120v-720h720v720H120Zm60-500h600v-160H180v160Zm220 220h160v-160H400v160Zm0 220h160v-160H400v160ZM180-400h160v-160H180v160Zm440 0h160v-160H620v160ZM180-180h160v-160H180v160Zm440 0h160v-160H620v160Z\"/>\n", "  </svg>\n", "    </button>\n", "\n", "  <style>\n", "    .colab-df-container {\n", "      display:flex;\n", "      gap: 12px;\n", "    }\n", "\n", "    .colab-df-convert {\n", "      background-color: #E8F0FE;\n", "      border: none;\n", "      border-radius: 50%;\n", "      cursor: pointer;\n", "      display: none;\n", "      fill: #1967D2;\n", "      height: 32px;\n", "      padding: 0 0 0 0;\n", "      width: 32px;\n", "    }\n", "\n", "    .colab-df-convert:hover {\n", "      background-color: #E2EBFA;\n", "      box-shadow: 0px 1px 2px rgba(60, 64, 67, 0.3), 0px 1px 3px 1px rgba(60, 64, 67, 0.15);\n", "      fill: #174EA6;\n", "    }\n", "\n", "    .colab-df-buttons div {\n", "      margin-bottom: 4px;\n", "    }\n", "\n", "    [theme=dark] .colab-df-convert {\n", "      background-color: #3B4455;\n", "      fill: #D2E3FC;\n", "    }\n", "\n", "    [theme=dark] .colab-df-convert:hover {\n", "      background-color: #434B5C;\n", "      box-shadow: 0px 1px 3px 1px rgba(0, 0, 0, 0.15);\n", "      filter: drop-shadow(0px 1px 2px rgba(0, 0, 0, 0.3));\n", "      fill: #FFFFFF;\n", "    }\n", "  </style>\n", "\n", "    <script>\n", "      const buttonEl =\n", "        document.querySelector('#df-dcc94098-ae18-439a-ac3e-137fb89376de button.colab-df-convert');\n", "      buttonEl.style.display =\n", "        google.colab.kernel.accessAllowed ? 'block' : 'none';\n", "\n", "      async function convertToInteractive(key) {\n", "        const element = document.querySelector('#df-dcc94098-ae18-439a-ac3e-137fb89376de');\n", "        const dataTable =\n", "          await google.colab.kernel.invokeFunction('convertToInteractive',\n", "                                                    [key], {});\n", "        if (!dataTable) return;\n", "\n", "        const docLinkHtml = 'Like what you see? Visit the ' +\n", "          '<a target=\"_blank\" href=https://colab.research.google.com/notebooks/data_table.ipynb>data table notebook</a>'\n", "          + ' to learn more about interactive tables.';\n", "        element.innerHTML = '';\n", "        dataTable['output_type'] = 'display_data';\n", "        await google.colab.output.renderOutput(dataTable, element);\n", "        const docLink = document.createElement('div');\n", "        docLink.innerHTML = docLinkHtml;\n", "        element.appendChild(docLink);\n", "      }\n", "    </script>\n", "  </div>\n", "\n", "\n", "    <div id=\"df-20011e15-e706-4948-ab67-fc5ecf7cf017\">\n", "      <button class=\"colab-df-quickchart\" onclick=\"quickchart('df-20011e15-e706-4948-ab67-fc5ecf7cf017')\"\n", "                title=\"Suggest charts\"\n", "                style=\"display:none;\">\n", "\n", "<svg xmlns=\"http://www.w3.org/2000/svg\" height=\"24px\"viewBox=\"0 0 24 24\"\n", "     width=\"24px\">\n", "    <g>\n", "        <path d=\"M19 3H5c-1.1 0-2 .9-2 2v14c0 1.1.9 2 2 2h14c1.1 0 2-.9 2-2V5c0-1.1-.9-2-2-2zM9 17H7v-7h2v7zm4 0h-2V7h2v10zm4 0h-2v-4h2v4z\"/>\n", "    </g>\n", "</svg>\n", "      </button>\n", "\n", "<style>\n", "  .colab-df-quickchart {\n", "      --bg-color: #E8F0FE;\n", "      --fill-color: #1967D2;\n", "      --hover-bg-color: #E2EBFA;\n", "      --hover-fill-color: #174EA6;\n", "      --disabled-fill-color: #AAA;\n", "      --disabled-bg-color: #DDD;\n", "  }\n", "\n", "  [theme=dark] .colab-df-quickchart {\n", "      --bg-color: #3B4455;\n", "      --fill-color: #D2E3FC;\n", "      --hover-bg-color: #434B5C;\n", "      --hover-fill-color: #FFFFFF;\n", "      --disabled-bg-color: #3B4455;\n", "      --disabled-fill-color: #666;\n", "  }\n", "\n", "  .colab-df-quickchart {\n", "    background-color: var(--bg-color);\n", "    border: none;\n", "    border-radius: 50%;\n", "    cursor: pointer;\n", "    display: none;\n", "    fill: var(--fill-color);\n", "    height: 32px;\n", "    padding: 0;\n", "    width: 32px;\n", "  }\n", "\n", "  .colab-df-quickchart:hover {\n", "    background-color: var(--hover-bg-color);\n", "    box-shadow: 0 1px 2px rgba(60, 64, 67, 0.3), 0 1px 3px 1px rgba(60, 64, 67, 0.15);\n", "    fill: var(--button-hover-fill-color);\n", "  }\n", "\n", "  .colab-df-quickchart-complete:disabled,\n", "  .colab-df-quickchart-complete:disabled:hover {\n", "    background-color: var(--disabled-bg-color);\n", "    fill: var(--disabled-fill-color);\n", "    box-shadow: none;\n", "  }\n", "\n", "  .colab-df-spinner {\n", "    border: 2px solid var(--fill-color);\n", "    border-color: transparent;\n", "    border-bottom-color: var(--fill-color);\n", "    animation:\n", "      spin 1s steps(1) infinite;\n", "  }\n", "\n", "  @keyframes spin {\n", "    0% {\n", "      border-color: transparent;\n", "      border-bottom-color: var(--fill-color);\n", "      border-left-color: var(--fill-color);\n", "    }\n", "    20% {\n", "      border-color: transparent;\n", "      border-left-color: var(--fill-color);\n", "      border-top-color: var(--fill-color);\n", "    }\n", "    30% {\n", "      border-color: transparent;\n", "      border-left-color: var(--fill-color);\n", "      border-top-color: var(--fill-color);\n", "      border-right-color: var(--fill-color);\n", "    }\n", "    40% {\n", "      border-color: transparent;\n", "      border-right-color: var(--fill-color);\n", "      border-top-color: var(--fill-color);\n", "    }\n", "    60% {\n", "      border-color: transparent;\n", "      border-right-color: var(--fill-color);\n", "    }\n", "    80% {\n", "      border-color: transparent;\n", "      border-right-color: var(--fill-color);\n", "      border-bottom-color: var(--fill-color);\n", "    }\n", "    90% {\n", "      border-color: transparent;\n", "      border-bottom-color: var(--fill-color);\n", "    }\n", "  }\n", "</style>\n", "\n", "      <script>\n", "        async function quickchart(key) {\n", "          const quickchartButtonEl =\n", "            document.querySelector('#' + key + ' button');\n", "          quickchartButtonEl.disabled = true;  // To prevent multiple clicks.\n", "          quickchartButtonEl.classList.add('colab-df-spinner');\n", "          try {\n", "            const charts = await google.colab.kernel.invokeFunction(\n", "                'suggest<PERSON><PERSON>s', [key], {});\n", "          } catch (error) {\n", "            console.error('Error during call to suggest<PERSON>harts:', error);\n", "          }\n", "          quickchartButtonEl.classList.remove('colab-df-spinner');\n", "          quickchartButtonEl.classList.add('colab-df-quickchart-complete');\n", "        }\n", "        (() => {\n", "          let quickchartButtonEl =\n", "            document.querySelector('#df-20011e15-e706-4948-ab67-fc5ecf7cf017 button');\n", "          quickchartButtonEl.style.display =\n", "            google.colab.kernel.accessAllowed ? 'block' : 'none';\n", "        })();\n", "      </script>\n", "    </div>\n", "\n", "    </div>\n", "  </div>\n"], "application/vnd.google.colaboratory.intrinsic+json": {"type": "dataframe", "variable_name": "df_encoded"}}, "metadata": {}, "execution_count": 16}]}, {"cell_type": "code", "source": ["df_encoded.info()"], "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "85YXI-xpIQwy", "outputId": "63e95a8e-ba50-4233-fba7-c65b04158020"}, "execution_count": 17, "outputs": [{"output_type": "stream", "name": "stdout", "text": ["<class 'pandas.core.frame.DataFrame'>\n", "RangeIndex: 1197 entries, 0 to 1196\n", "Columns: 253 entries, id to exterior_material_Wood\n", "dtypes: bool(204), float64(31), int64(18)\n", "memory usage: 696.8 KB\n"]}]}, {"cell_type": "code", "source": [], "metadata": {"id": "HtQV2ztzGPJi"}, "execution_count": 17, "outputs": []}, {"cell_type": "code", "source": [], "metadata": {"id": "8jNvK47JGP2O"}, "execution_count": 17, "outputs": []}, {"cell_type": "code", "source": [], "metadata": {"id": "fTpvmfsaGQD7"}, "execution_count": 17, "outputs": []}, {"cell_type": "code", "source": [], "metadata": {"id": "zQFg4D8NGQRm"}, "execution_count": 17, "outputs": []}, {"cell_type": "code", "source": [], "metadata": {"id": "hpAtBGOEGQmq"}, "execution_count": 17, "outputs": []}, {"cell_type": "markdown", "source": ["# 4. Define Feature Matrix (X) and Target Vector (Y)\n", "\n", "* Separate predictors from the target `mssubclass`"], "metadata": {"id": "spf_z7bv1xjR"}}, {"cell_type": "code", "source": ["# Define Feature Matrix (X) and Target Vector (Y)\n", "\n", "# Print the columns of df_encoded to verify the column names\n", "print(\"Columns in df_encoded:\", df_encoded.columns.tolist())\n", "\n", "# Check if 'SalePrice' column exists before dropping\n", "# The column name in the DataFrame is 'saleprice' (lowercase)\n", "if 'saleprice' in df_encoded.columns:\n", "    X = df_encoded.drop(\"saleprice\", axis=1) # Use lowercase 'saleprice'\n", "    y = df_encoded[\"saleprice\"] # Use lowercase 'saleprice'\n", "    print(\"X shape:\", X.shape)\n", "    print(\"y shape:\", y.shape)\n", "else:\n", "    # This part of the code will now check for the correct lowercase column name\n", "    print(\"Error: 'saleprice' column not found in df_encoded. Please check previous steps.\")\n", "df_encoded.columns.tolist()"], "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "XjcRmRqVJM7c", "outputId": "eae24b2f-c8b7-4a09-c222-1c9b337b914a"}, "execution_count": 18, "outputs": [{"output_type": "stream", "name": "stdout", "text": ["Columns in df_encoded: ['id', 'lotfrontage', 'lotarea', 'overallqual', 'overallcond', 'yearbuilt', 'yearremodadd', 'masvnrarea', 'bsmtfinsf1', 'bsmtfinsf2', 'bsmtunfsf', 'totalbsmtsf', '1stflrsf', '2ndflrsf', 'lowqualfinsf', 'grlivarea', 'bsmtfullbath', 'bsmthalfbath', 'fullbath', 'halfbath', 'bedroomabvgr', 'kitchenabvgr', 'totrmsabvgrd', 'fireplaces', 'garageyrblt', 'garagecars', 'garagearea', 'wooddecksf', 'openporchsf', 'enclosedporch', '3ssnporch', 'screenporch', 'poolarea', 'miscval', 'mosold', 'yrsold', 'saleprice', 'TotalSF', 'TotalBathrooms', 'TotalPorchSF', 'IsNew', 'HasPool', 'HasGarage', 'HasBasement', 'HasPorch', 'hasshed', 'QualxAge', 'HouseAge_sq', 'NeighborhoodPrice', 'mssubclass_3.4339872044851463', 'mssubclass_3.713572066704308', 'mssubclass_3.828641396489095', 'mssubclass_3.9318256327243257', 'mssubclass_4.110873864173311', 'mssubclass_4.262679877041316', 'mssubclass_4.330733340286331', 'mssubclass_4.394449154672439', 'mssubclass_4.454347296253507', 'mssubclass_4.51085950651685', 'mssubclass_4.795790545596741', 'mssubclass_5.081404364984463', 'mssubclass_5.198497031265826', 'mssubclass_5.25227342804663', 'mszoning_FV', 'mszoning_RH', 'mszoning_RL', 'mszoning_RM', 'street_Pave', 'alley_Pave', 'lotshape_IR2', 'lotshape_IR3', 'lotshape_Reg', 'landcontour_HLS', 'landcontour_Low', 'landcontour_Lvl', 'lotconfig_CulDSac', 'lotconfig_FR2', 'lotconfig_FR3', 'lotconfig_Inside', 'landslope_sloped', 'neighborhood_Blueste', 'neighborhood_BrDale', 'neighborhood_BrkSide', 'neighborhood_ClearCr', 'neighborhood_CollgCr', 'neighborhood_Crawfor', 'neighborhood_Edwards', 'neighborhood_Gilbert', 'neighborhood_IDOTRR', 'neighborhood_MeadowV', 'neighborhood_Mitchel', 'neighborhood_NAmes', 'neighborhood_NPkVill', 'neighborhood_NWAmes', 'neighborhood_NoRidge', 'neighborhood_NridgHt', 'neighborhood_OldTown', 'neighborhood_SWISU', 'neighborhood_Sawyer', 'neighborhood_SawyerW', 'neighborhood_Somerst', 'neighborhood_StoneBr', 'neighborhood_Timber', 'neighborhood_Veenker', 'condition1_Feedr', 'condition1_Norm', 'condition1_PosA', 'condition1_PosN', 'condition1_RRAe', 'condition1_RRAn', 'condition1_RRNe', 'condition1_RRNn', 'condition2_Feedr', 'condition2_Norm', 'condition2_PosA', 'condition2_PosN', 'condition2_RRNn', 'bldgtype_2fmCon', 'bldgtype_Duplex', 'bldgtype_Twnhs', 'bldgtype_TwnhsE', 'housestyle_1.5Unf', 'housestyle_1Story', 'housestyle_2.5Fin', 'housestyle_2.5Unf', 'housestyle_2Story', 'housestyle_SFoyer', 'housestyle_SLvl', 'roofstyle_Gable', 'roofstyle_Gambrel', 'roofstyle_Hip', 'roofstyle_Mansard', 'roofmatl_Membran', 'roofmatl_Roll', 'roofmatl_Tar&Grv', 'roofmatl_WdShake', 'roofmatl_WdShngl', 'exterior1st_AsphShn', 'exterior1st_BrkComm', 'exterior1st_BrkFace', 'exterior1st_CBlock', 'exterior1st_CemntBd', 'exterior1st_HdBoard', 'exterior1st_ImStucc', 'exterior1st_MetalSd', 'exterior1st_Plywood', 'exterior1st_Stone', 'exterior1st_Stucco', 'exterior1st_VinylSd', 'exterior1st_Wd Sdng', 'exterior1st_WdShing', 'masvnrtype_BrkFace', 'masvnrtype_Stone', 'exterqual_Fa', 'exterqual_Gd', 'exterqual_TA', 'extercond_Fa', 'extercond_Gd', 'extercond_Po', 'extercond_TA', 'foundation_CBlock', 'foundation_PConc', 'foundation_Slab', 'foundation_Stone', 'foundation_Wood', 'bsmtqual_Fa', 'bsmtqual_Gd', 'bsmtqual_TA', 'bsmtcond_Gd', 'bsmtcond_Po', 'bsmtcond_TA', 'bsmtexposure_Gd', 'bsmtexposure_Mn', 'bsmtexposure_No', 'bsmtfintype1_BLQ', 'bsmtfintype1_GLQ', 'bsmtfintype1_LwQ', 'bsmtfintype1_Rec', 'bsmtfintype1_Unf', 'bsmtfintype2_BLQ', 'bsmtfintype2_GLQ', 'bsmtfintype2_LwQ', 'bsmtfintype2_Rec', 'bsmtfintype2_Unf', 'heating_GasW', 'heating_Grav', 'heating_OthW', 'heating_Wall', 'heatingqc_Fa', 'heatingqc_Gd', 'heatingqc_Po', 'heatingqc_TA', 'centralair_Y', 'electrical_FuseF', 'electrical_FuseP', 'electrical_Mix', 'electrical_SBrkr', 'kitchenqual_Fa', 'kitchenqual_Gd', 'kitchenqual_TA', 'functional_Maj2', 'functional_Min1', 'functional_Min2', 'functional_Mod', 'functional_Typ', 'fireplacequ_Fa', 'fireplacequ_Gd', 'fireplacequ_Po', 'fireplacequ_TA', 'garagetype_Attchd', 'garagetype_Basment', 'garagetype_BuiltIn', 'garagetype_CarPort', 'garagetype_Detchd', 'garagefinish_RFn', 'garagefinish_Unf', 'garagequal_Fa', 'garagequal_Gd', 'garagequal_Po', 'garagequal_TA', 'garagecond_Fa', 'garagecond_Gd', 'garagecond_Po', 'garagecond_TA', 'paveddrive_P', 'paveddrive_Y', 'poolqc_Fa', 'poolqc_Gd', 'fence_GdWo', 'fence_MnPrv', 'fence_MnWw', 'saletype_CWD', 'saletype_Con', 'saletype_ConLD', 'saletype_ConLI', 'saletype_ConLw', 'saletype_New', 'saletype_Oth', 'saletype_WD', 'salecondition_AdjLand', 'salecondition_Alloca', 'salecondition_Family', 'salecondition_Normal', 'salecondition_Partial', 'exterior_material_Asphalt', 'exterior_material_Brick', 'exterior_material_CBlock', 'exterior_material_Cement', 'exterior_material_Metal', 'exterior_material_Stone', 'exterior_material_Stucco', 'exterior_material_Vinyl', 'exterior_material_Wood']\n", "X shape: (1197, 252)\n", "y shape: (1197,)\n"]}, {"output_type": "execute_result", "data": {"text/plain": ["['id',\n", " 'lotfrontage',\n", " 'lotarea',\n", " 'overallqual',\n", " 'overallcond',\n", " 'yearbuilt',\n", " 'yearremodadd',\n", " 'masvnrarea',\n", " 'bsmtfinsf1',\n", " 'bsmtfinsf2',\n", " 'bsmtunfsf',\n", " 'totalbsmtsf',\n", " '1stflrsf',\n", " '2ndflrsf',\n", " 'lowqualfinsf',\n", " 'grlivarea',\n", " 'bsmtfullbath',\n", " 'bsmthalfbath',\n", " 'fullbath',\n", " 'halfbath',\n", " 'bedroomabvgr',\n", " 'kitchenabvgr',\n", " 'totrmsabvgrd',\n", " 'fireplaces',\n", " 'garageyrblt',\n", " 'garagecars',\n", " 'garagearea',\n", " 'wooddecksf',\n", " 'openporchsf',\n", " 'enclosedporch',\n", " '3ssnporch',\n", " 'screenporch',\n", " 'poolarea',\n", " 'miscval',\n", " 'mosold',\n", " 'yrsold',\n", " 'saleprice',\n", " 'TotalSF',\n", " 'TotalBathrooms',\n", " 'TotalPorchSF',\n", " 'IsNew',\n", " 'HasPool',\n", " 'HasGarage',\n", " 'HasBasement',\n", " 'HasPorch',\n", " 'hasshed',\n", " 'QualxAge',\n", " 'HouseAge_sq',\n", " 'NeighborhoodPrice',\n", " 'mssubclass_3.4339872044851463',\n", " 'mssubclass_3.713572066704308',\n", " 'mssubclass_3.828641396489095',\n", " 'mssubclass_3.9318256327243257',\n", " 'mssubclass_4.110873864173311',\n", " 'mssubclass_4.262679877041316',\n", " 'mssubclass_4.330733340286331',\n", " 'mssubclass_4.394449154672439',\n", " 'mssubclass_4.454347296253507',\n", " 'mssubclass_4.51085950651685',\n", " 'mssubclass_4.795790545596741',\n", " 'mssubclass_5.081404364984463',\n", " 'mssubclass_5.198497031265826',\n", " 'mssubclass_5.25227342804663',\n", " 'mszoning_FV',\n", " 'mszoning_RH',\n", " 'mszoning_RL',\n", " 'mszoning_RM',\n", " 'street_Pave',\n", " 'alley_Pave',\n", " 'lotshape_IR2',\n", " 'lotshape_IR3',\n", " 'lotshape_Reg',\n", " 'landcontour_HLS',\n", " 'landcontour_Low',\n", " 'landcontour_Lvl',\n", " 'lotconfig_CulDSac',\n", " 'lotconfig_FR2',\n", " 'lotconfig_FR3',\n", " 'lotconfig_Inside',\n", " 'landslope_sloped',\n", " 'neighborhood_Blueste',\n", " 'neighborhood_BrDale',\n", " 'neighborhood_BrkSide',\n", " 'neighborhood_ClearCr',\n", " 'neighborhood_CollgCr',\n", " 'neighborhood_Crawfor',\n", " 'neighborhood_Edwards',\n", " 'neighborhood_Gilbert',\n", " 'neighborhood_IDOTRR',\n", " 'neighborhood_MeadowV',\n", " 'neighborhood_Mitchel',\n", " 'neighborhood_NAmes',\n", " 'neighborhood_NPkVill',\n", " 'neighborhood_NWAmes',\n", " 'neighborhood_NoRidge',\n", " 'neighborhood_NridgHt',\n", " 'neighborhood_OldTown',\n", " 'neighborhood_SWISU',\n", " 'neighborhood_Sawyer',\n", " 'neighborhood_SawyerW',\n", " 'neighborhood_Somerst',\n", " 'neighborhood_StoneBr',\n", " 'neighborhood_Timber',\n", " 'neighborhood_Veenker',\n", " 'condition1_Feedr',\n", " 'condition1_Norm',\n", " 'condition1_PosA',\n", " 'condition1_PosN',\n", " 'condition1_RRAe',\n", " 'condition1_RRAn',\n", " 'condition1_RRNe',\n", " 'condition1_RRNn',\n", " 'condition2_Feedr',\n", " 'condition2_Norm',\n", " 'condition2_PosA',\n", " 'condition2_PosN',\n", " 'condition2_RRNn',\n", " 'bldgtype_2fmCon',\n", " 'bldgtype_Duplex',\n", " 'bldgtype_Twnhs',\n", " 'bldgtype_TwnhsE',\n", " 'housestyle_1.5Unf',\n", " 'housestyle_1Story',\n", " 'housestyle_2.5Fin',\n", " 'housestyle_2.5Unf',\n", " 'housestyle_2Story',\n", " 'housestyle_SFoyer',\n", " 'housestyle_SLvl',\n", " 'roofstyle_Gable',\n", " 'roofstyle_Gambrel',\n", " 'roofstyle_Hip',\n", " 'roofstyle_Mansard',\n", " 'roofmatl_Membran',\n", " 'roofmatl_Roll',\n", " 'roofmatl_Tar&Grv',\n", " 'roofmatl_WdShake',\n", " 'roofmatl_WdShngl',\n", " 'exterior1st_AsphShn',\n", " 'exterior1st_BrkComm',\n", " 'exterior1st_BrkFace',\n", " 'exterior1st_CBlock',\n", " 'exterior1st_CemntBd',\n", " 'exterior1st_HdBoard',\n", " 'exterior1st_ImStucc',\n", " 'exterior1st_MetalSd',\n", " 'exterior1st_Plywood',\n", " 'exterior1st_Stone',\n", " 'exterior1st_Stucco',\n", " 'exterior1st_VinylSd',\n", " 'exterior1st_Wd Sdng',\n", " 'exterior1st_WdShing',\n", " 'masvnrtype_BrkFace',\n", " 'masvnrtype_Stone',\n", " 'exterqual_Fa',\n", " 'exterqual_Gd',\n", " 'exterqual_TA',\n", " 'extercond_Fa',\n", " 'extercond_Gd',\n", " 'extercond_Po',\n", " 'extercond_TA',\n", " 'foundation_CBlock',\n", " 'foundation_PConc',\n", " 'foundation_Slab',\n", " 'foundation_Stone',\n", " 'foundation_Wood',\n", " 'bsmtqual_Fa',\n", " 'bsmtqual_Gd',\n", " 'bsmtqual_TA',\n", " 'bsmtcond_Gd',\n", " 'bsmtcond_Po',\n", " 'bsmtcond_TA',\n", " 'bsmtexposure_Gd',\n", " 'bsmtexposure_Mn',\n", " 'bsmtexposure_No',\n", " 'bsmtfintype1_BLQ',\n", " 'bsmtfintype1_GLQ',\n", " 'bsmtfintype1_LwQ',\n", " 'bsmtfintype1_Rec',\n", " 'bsmtfintype1_Unf',\n", " 'bsmtfintype2_BLQ',\n", " 'bsmtfintype2_GLQ',\n", " 'bsmtfintype2_LwQ',\n", " 'bsmtfintype2_Rec',\n", " 'bsmtfintype2_Unf',\n", " 'heating_GasW',\n", " 'heating_Grav',\n", " 'heating_OthW',\n", " 'heating_Wall',\n", " 'heatingqc_Fa',\n", " 'heatingqc_Gd',\n", " 'heatingqc_Po',\n", " 'heatingqc_TA',\n", " 'centralair_Y',\n", " 'electrical_FuseF',\n", " 'electrical_FuseP',\n", " 'electrical_Mix',\n", " 'electrical_SBrkr',\n", " 'kitchenqual_Fa',\n", " 'kitchenqual_Gd',\n", " 'kitchenqual_TA',\n", " 'functional_Maj2',\n", " 'functional_Min1',\n", " 'functional_Min2',\n", " 'functional_Mod',\n", " 'functional_Typ',\n", " 'fireplacequ_Fa',\n", " 'fireplacequ_Gd',\n", " 'fireplacequ_Po',\n", " 'fireplacequ_TA',\n", " 'garagetype_Attchd',\n", " 'garagetype_Basment',\n", " 'garagetype_BuiltIn',\n", " 'garagetype_CarPort',\n", " 'garagetype_Detchd',\n", " 'garagefinish_RFn',\n", " 'garagefinish_Unf',\n", " 'garagequal_Fa',\n", " 'garagequal_Gd',\n", " 'garagequal_Po',\n", " 'garagequal_TA',\n", " 'garagecond_Fa',\n", " 'garagecond_Gd',\n", " 'garagecond_Po',\n", " 'garagecond_TA',\n", " 'paveddrive_P',\n", " 'paveddrive_Y',\n", " 'poolqc_Fa',\n", " 'poolqc_Gd',\n", " 'fence_GdWo',\n", " 'fence_MnPrv',\n", " 'fence_MnWw',\n", " 'saletype_CWD',\n", " 'saletype_Con',\n", " 'saletype_ConLD',\n", " 'saletype_ConLI',\n", " 'saletype_ConLw',\n", " 'saletype_New',\n", " 'saletype_Oth',\n", " 'saletype_WD',\n", " 'salecondition_AdjLand',\n", " 'salecondition_Alloca',\n", " 'salecondition_Family',\n", " 'salecondition_Normal',\n", " 'salecondition_Partial',\n", " 'exterior_material_Asphalt',\n", " 'exterior_material_Brick',\n", " 'exterior_material_CBlock',\n", " 'exterior_material_Cement',\n", " 'exterior_material_Metal',\n", " 'exterior_material_Stone',\n", " 'exterior_material_Stucco',\n", " 'exterior_material_Vinyl',\n", " 'exterior_material_Wood']"]}, "metadata": {}, "execution_count": 18}]}, {"cell_type": "code", "source": ["# Define Feature Matrix (X) and Target Vector (Y)\n", "X = df_encoded.drop(\"saleprice\", axis=1)\n", "y = df_encoded[\"saleprice\"]\n", "print(\"X shape:\", X.shape)\n", "print(\"y shape:\", y.shape)"], "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "qjm5LBfwzwQE", "outputId": "d2e9de44-e090-40e5-8791-c447d4a887bd"}, "execution_count": 19, "outputs": [{"output_type": "stream", "name": "stdout", "text": ["X shape: (1197, 252)\n", "y shape: (1197,)\n"]}]}, {"cell_type": "markdown", "source": ["# 5. Train/test Split\n", "\n", "* Hold out 20% of the data as a test set to evaluate out-of-sample performance."], "metadata": {"id": "gaDmsQvd3xrd"}}, {"cell_type": "code", "source": ["xtrain, xtest, ytrain, ytest = train_test_split(X, y, test_size=0.20, random_state=42)\n", "print(\"Train set:\", xtrain.shape, ytrain.shape)\n", "print(\"Test set: \", xtest.shape, ytest.shape)"], "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "PwXQnDGbzwXv", "outputId": "b83103de-c692-414e-c0f2-bf1ae116526a"}, "execution_count": 20, "outputs": [{"output_type": "stream", "name": "stdout", "text": ["Train set: (957, 252) (957,)\n", "Test set:  (240, 252) (240,)\n"]}]}, {"cell_type": "markdown", "source": ["# 6. <PERSON>\n", "\n", "* Standardize features to zero mean and unit variance. This is important for linear models with regularization."], "metadata": {"id": "hQzIKoDN4XXA"}}, {"cell_type": "code", "source": ["scaler = StandardScaler()\n", "xtrain_scaled = scaler.fit_transform(xtrain)\n", "xtest_scaled  = scaler.transform(xtest)"], "metadata": {"id": "iivaaCXDzwfW"}, "execution_count": 21, "outputs": []}, {"cell_type": "markdown", "source": ["#7. <PERSON><PERSON> Model (Predict the Mean)\n", "\n", "* A `DummyRegressor` that always predicts the mean sale price. Provides a performance floor—any useful model should beat this."], "metadata": {"id": "0swqpUde5tw-"}}, {"cell_type": "code", "source": ["dummy_reg    = DummyRegressor(strategy=\"mean\")\n", "dummy_reg.fit(xtrain_scaled, ytrain)\n", "y_base_pred  = dummy_reg.predict(xtest_scaled)\n", "baseline_rmse = np.sqrt(mean_squared_error(ytest, y_base_pred))\n", "print(\"Baseline RMSE on test set: ${:,.0f}\".format(baseline_rmse))"], "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "feWTwphOzwpR", "outputId": "99193df4-25ff-44dd-f381-7df978484c01"}, "execution_count": 22, "outputs": [{"output_type": "stream", "name": "stdout", "text": ["Baseline RMSE on test set: $75,622\n"]}]}, {"cell_type": "markdown", "source": ["## 8. OLS Linear Regression\n", "\n", "Fit a standard unregularized linear regression to predict `SalePrice`. Monitor R² and RMSE on the test set."], "metadata": {"id": "8jyOo546B0H2"}}, {"cell_type": "code", "source": ["# Linear Regression Model\n", "from sklearn.impute import SimpleImputer\n", "\n", "# Impute missing values using the mean\n", "# Create an imputer object\n", "imputer = SimpleImputer(strategy='mean')\n", "\n", "# Fit the imputer on the training data and transform both training and test data\n", "xtrain_imputed = imputer.fit_transform(xtrain)\n", "xtest_imputed = imputer.transform(xtest)\n", "\n", "lr = LinearRegression()\n", "# Fit the model on the imputed training data\n", "lr.fit(xtrain_imputed, ytrain)\n", "# Predict on the imputed test data\n", "y_lr_pred    = lr.predict(xtest_imputed)\n", "lr_r2        = r2_score(ytest, y_lr_pred)\n", "lr_rmse      = np.sqrt(mean_squared_error(ytest, y_lr_pred))\n", "print(f\"\\nOLS LinearRegression → R²: {lr_r2:.4f}, RMSE: ${lr_rmse:,.0f}\")"], "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "GsEWES05zwwz", "outputId": "3d940128-3244-4dd3-bf63-9b33707fb401"}, "execution_count": 23, "outputs": [{"output_type": "stream", "name": "stdout", "text": ["\n", "OLS LinearRegression → R²: 0.8424, RMSE: $29,926\n"]}]}, {"cell_type": "markdown", "source": ["## 9. Lasso Regression with Cross-Validation (LassoCV)\n", "\n", "* Use `LassoCV` to automatically select the optimal L₁ penalty (α) via 5-fold CV."], "metadata": {"id": "yNQjWREV6tvf"}}, {"cell_type": "code", "source": ["# Lasso Regression with Cross-Validation (LassoCV)\n", "\n", "# Create a new scaler or re-fit the existing one on the imputed training data\n", "scaler_imputed = StandardScaler()\n", "xtrain_scaled_imputed = scaler_imputed.fit_transform(xtrain_imputed)\n", "xtest_scaled_imputed = scaler_imputed.transform(xtest_imputed)\n", "\n", "\n", "lasso_cv = LassoCV(\n", "    cv=5, random_state=42,\n", "    n_jobs=-1, max_iter=15_000\n", ")\n", "\n", "# Fit the model on the scaled and imputed training data\n", "lasso_cv.fit(xtrain_scaled_imputed, ytrain)\n", "\n", "# Predict on the scaled and imputed test data\n", "y_lasso_pred    = lasso_cv.predict(xtest_scaled_imputed)\n", "lasso_r2        = r2_score(ytest, y_lasso_pred)\n", "lasso_rmse      = np.sqrt(mean_squared_error(ytest, y_lasso_pred))\n", "\n", "print(f\"LassoCV chose alpha = {lasso_cv.alpha_:.5f}\")\n", "print(f\"LassoCV → R²: {lasso_r2:.4f}, RMSE: ${lasso_rmse:,.0f}\")"], "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "1zjvX4sK517N", "outputId": "ba2aa126-d64c-4a1e-c700-ba72a98ad158"}, "execution_count": 24, "outputs": [{"output_type": "stream", "name": "stdout", "text": ["LassoCV chose alpha = 223.38065\n", "LassoCV → R²: 0.8744, RMSE: $26,716\n"]}]}, {"cell_type": "markdown", "source": ["## 10. Ridge Regression with Cross-Validation (RidgeCV)\n", "\n", "* Use `RidgeCV` to select the best L₂ penalty (α) across a log-spaced grid via 5-fold CV."], "metadata": {"id": "jl6xPJKa79BF"}}, {"cell_type": "code", "source": ["alphas_ridge = np.logspace(-3, 3, 50)\n", "ridge_cv = RidgeCV(alphas=alphas_ridge, cv=5, scoring=\"neg_mean_squared_error\")\n", "# Fit the RidgeCV model on the scaled and imputed training data\n", "ridge_cv.fit(xtrain_scaled_imputed, ytrain)\n", "# Predict on the scaled and imputed test data\n", "y_ridge_pred  = ridge_cv.predict(xtest_scaled_imputed)\n", "ridge_r2      = r2_score(ytest, y_ridge_pred)\n", "ridge_rmse    = np.sqrt(mean_squared_error(ytest, y_ridge_pred))\n", "print(f\"RidgeCV chose alpha = {ridge_cv.alpha_:.5f}\")\n", "print(f\"RidgeCV → R²: {ridge_r2:.4f}, RMSE: ${ridge_rmse:,.0f}\")"], "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "yrta0Fg-52FJ", "outputId": "e1eacdc9-618b-42f5-beea-8369eeba592e"}, "execution_count": 25, "outputs": [{"output_type": "stream", "name": "stdout", "text": ["RidgeCV chose alpha = 10.98541\n", "RidgeCV → R²: 0.8628, RMSE: $27,920\n"]}]}, {"cell_type": "markdown", "source": ["## 11. ElasticNet Regression with Cross-Validation (ElasticNetCV)\n", "\n", "Use `ElasticNetCV` to select both α and `l1_ratio` via 5-fold CV, blending L₁ and L₂ penalties."], "metadata": {"id": "GQKpzQM8H2eK"}}, {"cell_type": "code", "source": ["# ElasticNet Regression with Cross-Validation (ElasticNetCV)\n", "\n", "enet_cv = ElasticNetCV(\n", "    l1_ratio=[0.1, 0.3, 0.5, 0.7, 0.9, 1.0],\n", "    cv=5, random_state=42, max_iter=15000, n_jobs=-1\n", ")\n", "# Fit the model on the scaled and imputed training data\n", "enet_cv.fit(xtrain_scaled_imputed, ytrain)\n", "# Predict on the scaled and imputed test data\n", "y_enet_pred    = enet_cv.predict(xtest_scaled_imputed)\n", "enet_r2        = r2_score(ytest, y_enet_pred)\n", "enet_rmse      = np.sqrt(mean_squared_error(ytest, y_enet_pred))\n", "print(f'ElasticNetCV chose alpha = {enet_cv.alpha_:.5f}, l1_ratio = {enet_cv.l1_ratio_:.2f}')\n", "print(f'ElasticNetCV → R²: {enet_r2:.4f}, RMSE: ${enet_rmse:,.0f}')"], "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "n5Mr4kIC52Uv", "outputId": "4d97f676-12cd-4d35-a669-1004f8242138"}, "execution_count": 26, "outputs": [{"output_type": "stream", "name": "stdout", "text": ["ElasticNetCV chose alpha = 223.38065, l1_ratio = 1.00\n", "ElasticNetCV → R²: 0.8744, RMSE: $26,716\n"]}]}, {"cell_type": "markdown", "source": ["## 12. Model Comparison Summary\n", "\n", "* Summarize the performance of all linear models (built-in CV) on the test set."], "metadata": {"id": "bCh3Ucq6IOj1"}}, {"cell_type": "code", "source": ["results = pd.DataFrame({\n", "    'Model': [\n", "        '<PERSON><PERSON> (Mean)',\n", "        'OLS LinearRegression',\n", "        'LassoCV',\n", "        'RidgeCV',\n", "        'ElasticNetCV'\n", "    ],\n", "    'Test R²': [\n", "        r2_score(ytest, y_base_pred),\n", "        lr_r2,\n", "        lasso_r2,\n", "        ridge_r2,\n", "        enet_r2\n", "    ],\n", "    'Test RMSE': [\n", "        baseline_rmse,\n", "        lr_rmse,\n", "        lasso_rmse,\n", "        ridge_rmse,\n", "        enet_rmse\n", "    ]\n", "}).set_index('Model')\n", "\n", "results"], "metadata": {"colab": {"base_uri": "https://localhost:8080/", "height": 238}, "id": "_xoxBrwR52hd", "outputId": "e56a9ea6-a19a-46ad-9ce1-7b2cf832510d"}, "execution_count": 27, "outputs": [{"output_type": "execute_result", "data": {"text/plain": ["                       Test R²     Test RMSE\n", "Model                                       \n", "Baseline (Mean)      -0.006230  75622.018753\n", "OLS LinearRegression  0.842418  29926.263756\n", "LassoCV               0.874416  26715.701815\n", "RidgeCV               0.862840  27919.907755\n", "ElasticNetCV          0.874416  26715.701815"], "text/html": ["\n", "  <div id=\"df-f3331bc7-92ff-41d0-af4f-cb830c44fdf6\" class=\"colab-df-container\">\n", "    <div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>Test R²</th>\n", "      <th>Test RMSE</th>\n", "    </tr>\n", "    <tr>\n", "      <th>Model</th>\n", "      <th></th>\n", "      <th></th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th><PERSON><PERSON> (Mean)</th>\n", "      <td>-0.006230</td>\n", "      <td>75622.018753</td>\n", "    </tr>\n", "    <tr>\n", "      <th>OLS LinearRegression</th>\n", "      <td>0.842418</td>\n", "      <td>29926.263756</td>\n", "    </tr>\n", "    <tr>\n", "      <th>LassoCV</th>\n", "      <td>0.874416</td>\n", "      <td>26715.701815</td>\n", "    </tr>\n", "    <tr>\n", "      <th>RidgeCV</th>\n", "      <td>0.862840</td>\n", "      <td>27919.907755</td>\n", "    </tr>\n", "    <tr>\n", "      <th>ElasticNetCV</th>\n", "      <td>0.874416</td>\n", "      <td>26715.701815</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>\n", "    <div class=\"colab-df-buttons\">\n", "\n", "  <div class=\"colab-df-container\">\n", "    <button class=\"colab-df-convert\" onclick=\"convertToInteractive('df-f3331bc7-92ff-41d0-af4f-cb830c44fdf6')\"\n", "            title=\"Convert this dataframe to an interactive table.\"\n", "            style=\"display:none;\">\n", "\n", "  <svg xmlns=\"http://www.w3.org/2000/svg\" height=\"24px\" viewBox=\"0 -960 960 960\">\n", "    <path d=\"M120-120v-720h720v720H120Zm60-500h600v-160H180v160Zm220 220h160v-160H400v160Zm0 220h160v-160H400v160ZM180-400h160v-160H180v160Zm440 0h160v-160H620v160ZM180-180h160v-160H180v160Zm440 0h160v-160H620v160Z\"/>\n", "  </svg>\n", "    </button>\n", "\n", "  <style>\n", "    .colab-df-container {\n", "      display:flex;\n", "      gap: 12px;\n", "    }\n", "\n", "    .colab-df-convert {\n", "      background-color: #E8F0FE;\n", "      border: none;\n", "      border-radius: 50%;\n", "      cursor: pointer;\n", "      display: none;\n", "      fill: #1967D2;\n", "      height: 32px;\n", "      padding: 0 0 0 0;\n", "      width: 32px;\n", "    }\n", "\n", "    .colab-df-convert:hover {\n", "      background-color: #E2EBFA;\n", "      box-shadow: 0px 1px 2px rgba(60, 64, 67, 0.3), 0px 1px 3px 1px rgba(60, 64, 67, 0.15);\n", "      fill: #174EA6;\n", "    }\n", "\n", "    .colab-df-buttons div {\n", "      margin-bottom: 4px;\n", "    }\n", "\n", "    [theme=dark] .colab-df-convert {\n", "      background-color: #3B4455;\n", "      fill: #D2E3FC;\n", "    }\n", "\n", "    [theme=dark] .colab-df-convert:hover {\n", "      background-color: #434B5C;\n", "      box-shadow: 0px 1px 3px 1px rgba(0, 0, 0, 0.15);\n", "      filter: drop-shadow(0px 1px 2px rgba(0, 0, 0, 0.3));\n", "      fill: #FFFFFF;\n", "    }\n", "  </style>\n", "\n", "    <script>\n", "      const buttonEl =\n", "        document.querySelector('#df-f3331bc7-92ff-41d0-af4f-cb830c44fdf6 button.colab-df-convert');\n", "      buttonEl.style.display =\n", "        google.colab.kernel.accessAllowed ? 'block' : 'none';\n", "\n", "      async function convertToInteractive(key) {\n", "        const element = document.querySelector('#df-f3331bc7-92ff-41d0-af4f-cb830c44fdf6');\n", "        const dataTable =\n", "          await google.colab.kernel.invokeFunction('convertToInteractive',\n", "                                                    [key], {});\n", "        if (!dataTable) return;\n", "\n", "        const docLinkHtml = 'Like what you see? Visit the ' +\n", "          '<a target=\"_blank\" href=https://colab.research.google.com/notebooks/data_table.ipynb>data table notebook</a>'\n", "          + ' to learn more about interactive tables.';\n", "        element.innerHTML = '';\n", "        dataTable['output_type'] = 'display_data';\n", "        await google.colab.output.renderOutput(dataTable, element);\n", "        const docLink = document.createElement('div');\n", "        docLink.innerHTML = docLinkHtml;\n", "        element.appendChild(docLink);\n", "      }\n", "    </script>\n", "  </div>\n", "\n", "\n", "    <div id=\"df-f151dc4b-a9a0-46d6-8e8a-c94b545cf063\">\n", "      <button class=\"colab-df-quickchart\" onclick=\"quickchart('df-f151dc4b-a9a0-46d6-8e8a-c94b545cf063')\"\n", "                title=\"Suggest charts\"\n", "                style=\"display:none;\">\n", "\n", "<svg xmlns=\"http://www.w3.org/2000/svg\" height=\"24px\"viewBox=\"0 0 24 24\"\n", "     width=\"24px\">\n", "    <g>\n", "        <path d=\"M19 3H5c-1.1 0-2 .9-2 2v14c0 1.1.9 2 2 2h14c1.1 0 2-.9 2-2V5c0-1.1-.9-2-2-2zM9 17H7v-7h2v7zm4 0h-2V7h2v10zm4 0h-2v-4h2v4z\"/>\n", "    </g>\n", "</svg>\n", "      </button>\n", "\n", "<style>\n", "  .colab-df-quickchart {\n", "      --bg-color: #E8F0FE;\n", "      --fill-color: #1967D2;\n", "      --hover-bg-color: #E2EBFA;\n", "      --hover-fill-color: #174EA6;\n", "      --disabled-fill-color: #AAA;\n", "      --disabled-bg-color: #DDD;\n", "  }\n", "\n", "  [theme=dark] .colab-df-quickchart {\n", "      --bg-color: #3B4455;\n", "      --fill-color: #D2E3FC;\n", "      --hover-bg-color: #434B5C;\n", "      --hover-fill-color: #FFFFFF;\n", "      --disabled-bg-color: #3B4455;\n", "      --disabled-fill-color: #666;\n", "  }\n", "\n", "  .colab-df-quickchart {\n", "    background-color: var(--bg-color);\n", "    border: none;\n", "    border-radius: 50%;\n", "    cursor: pointer;\n", "    display: none;\n", "    fill: var(--fill-color);\n", "    height: 32px;\n", "    padding: 0;\n", "    width: 32px;\n", "  }\n", "\n", "  .colab-df-quickchart:hover {\n", "    background-color: var(--hover-bg-color);\n", "    box-shadow: 0 1px 2px rgba(60, 64, 67, 0.3), 0 1px 3px 1px rgba(60, 64, 67, 0.15);\n", "    fill: var(--button-hover-fill-color);\n", "  }\n", "\n", "  .colab-df-quickchart-complete:disabled,\n", "  .colab-df-quickchart-complete:disabled:hover {\n", "    background-color: var(--disabled-bg-color);\n", "    fill: var(--disabled-fill-color);\n", "    box-shadow: none;\n", "  }\n", "\n", "  .colab-df-spinner {\n", "    border: 2px solid var(--fill-color);\n", "    border-color: transparent;\n", "    border-bottom-color: var(--fill-color);\n", "    animation:\n", "      spin 1s steps(1) infinite;\n", "  }\n", "\n", "  @keyframes spin {\n", "    0% {\n", "      border-color: transparent;\n", "      border-bottom-color: var(--fill-color);\n", "      border-left-color: var(--fill-color);\n", "    }\n", "    20% {\n", "      border-color: transparent;\n", "      border-left-color: var(--fill-color);\n", "      border-top-color: var(--fill-color);\n", "    }\n", "    30% {\n", "      border-color: transparent;\n", "      border-left-color: var(--fill-color);\n", "      border-top-color: var(--fill-color);\n", "      border-right-color: var(--fill-color);\n", "    }\n", "    40% {\n", "      border-color: transparent;\n", "      border-right-color: var(--fill-color);\n", "      border-top-color: var(--fill-color);\n", "    }\n", "    60% {\n", "      border-color: transparent;\n", "      border-right-color: var(--fill-color);\n", "    }\n", "    80% {\n", "      border-color: transparent;\n", "      border-right-color: var(--fill-color);\n", "      border-bottom-color: var(--fill-color);\n", "    }\n", "    90% {\n", "      border-color: transparent;\n", "      border-bottom-color: var(--fill-color);\n", "    }\n", "  }\n", "</style>\n", "\n", "      <script>\n", "        async function quickchart(key) {\n", "          const quickchartButtonEl =\n", "            document.querySelector('#' + key + ' button');\n", "          quickchartButtonEl.disabled = true;  // To prevent multiple clicks.\n", "          quickchartButtonEl.classList.add('colab-df-spinner');\n", "          try {\n", "            const charts = await google.colab.kernel.invokeFunction(\n", "                'suggest<PERSON><PERSON>s', [key], {});\n", "          } catch (error) {\n", "            console.error('Error during call to suggest<PERSON>harts:', error);\n", "          }\n", "          quickchartButtonEl.classList.remove('colab-df-spinner');\n", "          quickchartButtonEl.classList.add('colab-df-quickchart-complete');\n", "        }\n", "        (() => {\n", "          let quickchartButtonEl =\n", "            document.querySelector('#df-f151dc4b-a9a0-46d6-8e8a-c94b545cf063 button');\n", "          quickchartButtonEl.style.display =\n", "            google.colab.kernel.accessAllowed ? 'block' : 'none';\n", "        })();\n", "      </script>\n", "    </div>\n", "\n", "  <div id=\"id_4eb45713-cc94-4b1e-8585-6ee61026d2cf\">\n", "    <style>\n", "      .colab-df-generate {\n", "        background-color: #E8F0FE;\n", "        border: none;\n", "        border-radius: 50%;\n", "        cursor: pointer;\n", "        display: none;\n", "        fill: #1967D2;\n", "        height: 32px;\n", "        padding: 0 0 0 0;\n", "        width: 32px;\n", "      }\n", "\n", "      .colab-df-generate:hover {\n", "        background-color: #E2EBFA;\n", "        box-shadow: 0px 1px 2px rgba(60, 64, 67, 0.3), 0px 1px 3px 1px rgba(60, 64, 67, 0.15);\n", "        fill: #174EA6;\n", "      }\n", "\n", "      [theme=dark] .colab-df-generate {\n", "        background-color: #3B4455;\n", "        fill: #D2E3FC;\n", "      }\n", "\n", "      [theme=dark] .colab-df-generate:hover {\n", "        background-color: #434B5C;\n", "        box-shadow: 0px 1px 3px 1px rgba(0, 0, 0, 0.15);\n", "        filter: drop-shadow(0px 1px 2px rgba(0, 0, 0, 0.3));\n", "        fill: #FFFFFF;\n", "      }\n", "    </style>\n", "    <button class=\"colab-df-generate\" onclick=\"generateWithVariable('results')\"\n", "            title=\"Generate code using this dataframe.\"\n", "            style=\"display:none;\">\n", "\n", "  <svg xmlns=\"http://www.w3.org/2000/svg\" height=\"24px\"viewBox=\"0 0 24 24\"\n", "       width=\"24px\">\n", "    <path d=\"M7,19H8.4L18.45,9,17,7.55,7,17.6ZM5,21V16.75L18.45,3.32a2,2,0,0,1,2.83,0l1.4,1.43a1.91,1.91,0,0,1,.58,1.4,1.91,1.91,0,0,1-.58,1.4L9.25,21ZM18.45,9,17,7.55Zm-12,3A5.31,5.31,0,0,0,4.9,8.1,5.31,5.31,0,0,0,1,6.5,5.31,5.31,0,0,0,4.9,4.9,5.31,5.31,0,0,0,6.5,1,5.31,5.31,0,0,0,8.1,4.9,5.31,5.31,0,0,0,12,6.5,5.46,5.46,0,0,0,6.5,12Z\"/>\n", "  </svg>\n", "    </button>\n", "    <script>\n", "      (() => {\n", "      const buttonEl =\n", "        document.querySelector('#id_4eb45713-cc94-4b1e-8585-6ee61026d2cf button.colab-df-generate');\n", "      buttonEl.style.display =\n", "        google.colab.kernel.accessAllowed ? 'block' : 'none';\n", "\n", "      buttonEl.onclick = () => {\n", "        google.colab.notebook.generateWithVariable('results');\n", "      }\n", "      })();\n", "    </script>\n", "  </div>\n", "\n", "    </div>\n", "  </div>\n"], "application/vnd.google.colaboratory.intrinsic+json": {"type": "dataframe", "variable_name": "results", "summary": "{\n  \"name\": \"results\",\n  \"rows\": 5,\n  \"fields\": [\n    {\n      \"column\": \"Model\",\n      \"properties\": {\n        \"dtype\": \"string\",\n        \"num_unique_values\": 5,\n        \"samples\": [\n          \"OLS LinearRegression\",\n          \"ElasticNetCV\",\n          \"LassoCV\"\n        ],\n        \"semantic_type\": \"\",\n        \"description\": \"\"\n      }\n    },\n    {\n      \"column\": \"Test R\\u00b2\",\n      \"properties\": {\n        \"dtype\": \"number\",\n        \"std\": 0.38918465409740316,\n        \"min\": -0.006229996368665169,\n        \"max\": 0.874416118730988,\n        \"num_unique_values\": 4,\n        \"samples\": [\n          0.8424183205391346,\n          0.8628396185206717,\n          -0.006229996368665169\n        ],\n        \"semantic_type\": \"\",\n        \"description\": \"\"\n      }\n    },\n    {\n      \"column\": \"Test RMSE\",\n      \"properties\": {\n        \"dtype\": \"number\",\n        \"std\": 21418.205101457006,\n        \"min\": 26715.701815334785,\n        \"max\": 75622.01875276642,\n        \"num_unique_values\": 4,\n        \"samples\": [\n          29926.26375642111,\n          27919.907754857137,\n          75622.01875276642\n        ],\n        \"semantic_type\": \"\",\n        \"description\": \"\"\n      }\n    }\n  ]\n}"}}, "metadata": {}, "execution_count": 27}]}, {"cell_type": "markdown", "source": ["## 13. Model Performance Comparison (Bar Charts)\n", "\n", "* Visualize Test RMSE and R² for each model."], "metadata": {"id": "A6skGwiIOuyp"}}, {"source": ["plt.figure(figsize=(8, 4))\n", "results['Test RMSE'].plot(kind='bar', color=['#1f77b4', '#ff7f0e', '#2ca02c', '#d62728', '#9467bd']) # Using tab10 color palette\n", "plt.ylabel('RMSE ($)')\n", "plt.title('Test RMSE for Each Model')\n", "plt.xticks(rotation=45)\n", "plt.tight_layout()\n", "plt.show()\n", "\n", "# --- Add a text separator with dotted lines ---\n", "print(\"\\n\" + \"=\"*20 + \"\" + \"=\"*70 + \"\\n\") # Simple text separator\n", "\n", "plt.figure(figsize=(8, 4))\n", "results['Test R²'].plot(kind='bar', color=['#1f77b4', '#ff7f0e', '#2ca02c', '#d62728', '#9467bd']) # Using tab10 color palette\n", "plt.ylabel('R²')\n", "plt.title('Test R² for Each Model')\n", "plt.xticks(rotation=45)\n", "plt.tight_layout()\n", "plt.show()"], "cell_type": "code", "execution_count": 28, "outputs": [{"output_type": "display_data", "data": {"text/plain": ["<Figure size 800x400 with 1 Axes>"], "image/png": "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\n"}, "metadata": {}}, {"output_type": "stream", "name": "stdout", "text": ["\n", "==========================================================================================\n", "\n"]}, {"output_type": "display_data", "data": {"text/plain": ["<Figure size 800x400 with 1 Axes>"], "image/png": "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\n"}, "metadata": {}}], "metadata": {"cellView": "form", "colab": {"base_uri": "https://localhost:8080/", "height": 849}, "id": "0ieGPYiyIiY6", "outputId": "55ff3d7f-e79b-48ea-af23-2534c9bbe303"}}, {"cell_type": "markdown", "source": ["## 14. LassoCV Mean Squared Error Path\n", "\n", "Plot the cross-validated mean MSE vs. α values for LassoCV."], "metadata": {"id": "ShpQ2tL2TWLE"}}, {"cell_type": "code", "source": ["plt.figure(figsize=(6, 4))\n", "# Replace 'viridis' with a valid single color string, e.g., 'blue'\n", "\n", "# Access the alpha values and mean MSE from the fitted lasso_cv object\n", "lasso_alphas = lasso_cv.alphas_\n", "mse_mean = lasso_cv.mse_path_.mean(axis=1) # Calculate the mean MSE across folds for each alpha\n", "\n", "plt.plot(lasso_alphas, mse_mean, marker='o', color='blue')\n", "plt.xscale('log')\n", "plt.gca().invert_xaxis()  # alpha decreases to the right\n", "plt.xlabel('Alpha')\n", "plt.ylabel('Mean MSE across folds')\n", "plt.title('LassoCV MSE Path')\n", "plt.grid(True)\n", "plt.tight_layout()\n", "plt.show()"], "metadata": {"colab": {"base_uri": "https://localhost:8080/", "height": 407}, "id": "PwIvjmTrTOBR", "outputId": "99733482-9ff9-43a8-ff3c-02ad2ef8f2bc"}, "execution_count": 29, "outputs": [{"output_type": "display_data", "data": {"text/plain": ["<Figure size 600x400 with 1 Axes>"], "image/png": "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\n"}, "metadata": {}}]}, {"cell_type": "markdown", "source": ["## 15. Extended Hyperparameter Tuning"], "metadata": {"id": "kD1MaFETW8eN"}}, {"cell_type": "markdown", "source": ["### 15.1 Lasso GridSearchCV\n", "\n", "We define a finer grid of `alpha` values and `max_iter` options for `<PERSON><PERSON>` and use `GridSearchCV` to find the best combination."], "metadata": {"id": "M-JSatKGczX0"}}, {"cell_type": "code", "source": ["from sklearn.impute import SimpleImputer\n", "from sklearn.model_selection  import train_test_split, GridSearchCV # Added GridSearchCV here\n", "from sklearn.preprocessing   import StandardScaler\n", "from sklearn.dummy           import DummyRegressor\n", "from sklearn.linear_model    import (\n", "    LinearRegression,\n", "    LassoCV, RidgeCV, ElasticNetCV, Lasso # Added Lasso here for GridSearchCV\n", ")\n", "from sklearn.metrics import mean_squared_error, r2_score\n", "from sklearn.metrics import r2_score, mean_squared_error\n", "\n", "# ... (rest of your imports and code)\n", "\n", "# Cell 15.1 Lasso GridSearchCV\n", "# Add the import for Las<PERSON> here as well if not already imported globally\n", "from sklearn.linear_model import Lasso\n", "# Add the import for GridSearchCV here as well if not already imported globally\n", "from sklearn.model_selection import GridSearchCV\n", "\n", "lasso_alpha_grid = np.logspace(-4, -1, 20)  # from 1e-4 to 1e-1\n", "lasso_params = {'alpha': lasso_alpha_grid, 'max_iter': [5000, 10000]}\n", "\n", "lasso_gs = GridSearchCV(\n", "    <PERSON><PERSON>(random_state=42),\n", "    param_grid=lasso_params,\n", "    cv=5,\n", "    scoring='neg_mean_squared_error',\n", "    n_jobs=-1\n", ")\n", "# Note: Based on previous code, it seems you intended to use the scaled data here.\n", "# Using xtrain_scaled_imputed and xtest_scaled_imputed as used in LassoCV, RidgeCV, ElasticNetCV\n", "lasso_gs.fit(xtrain_scaled_imputed, ytrain)\n", "best_lasso_alpha = lasso_gs.best_params_['alpha']\n", "best_lasso_iter  = lasso_gs.best_params_['max_iter']\n", "print(f'GridSearchCV Lasso best alpha: {best_lasso_alpha:.5f}, max_iter: {best_lasso_iter}')\n", "\n", "y_lasso_gs_pred = lasso_gs.predict(xtest_scaled_imputed) # Predict on scaled test data\n", "lasso_gs_r2 = r2_score(ytest, y_lasso_gs_pred)\n", "lasso_gs_rmse = np.sqrt(mean_squared_error(ytest, y_lasso_gs_pred))\n", "print(f'Lasso GridSearchCV → R²: {lasso_gs_r2:.4f}, RMSE: ${lasso_gs_rmse:,.0f}')"], "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "sWhXA7aSTOMI", "outputId": "8bea0305-ba58-4d8a-aeef-85aa1a364a96"}, "execution_count": 30, "outputs": [{"output_type": "stream", "name": "stdout", "text": ["GridSearchCV Lasso best alpha: 0.10000, max_iter: 10000\n", "Lasso GridSearchCV → R²: 0.8413, RMSE: $30,034\n"]}, {"output_type": "stream", "name": "stderr", "text": ["/usr/local/lib/python3.11/dist-packages/sklearn/linear_model/_coordinate_descent.py:695: ConvergenceWarning: Objective did not converge. You might want to increase the number of iterations, check the scale of the features or consider increasing regularisation. Duality gap: 1.200e+11, tolerance: 6.932e+08\n", "  model = cd_fast.enet_coordinate_descent(\n"]}]}, {"cell_type": "markdown", "source": ["### 15.2 Ridge GridSearchCV\n", "\n", "* We define a finer grid of `alpha` values around the `RidgeCV` result and explore different solvers for `Ridge`."], "metadata": {"id": "djN8QdXQe-Dk"}}, {"cell_type": "code", "source": ["from sklearn.linear_model import Ridge # Import Ridge here\n", "from sklearn.model_selection import GridSearchCV # Import GridSearchCV if not already available\n", "\n", "ridge_alpha_grid = np.logspace(-3, 2, 20)  # from 1e-3 to 1e2\n", "ridge_params = {'alpha': ridge_alpha_grid, 'solver': ['auto', 'svd', 'cholesky']}\n", "\n", "ridge_gs = GridSearchCV(\n", "    Ridge(random_state=42),\n", "    param_grid=ridge_params,\n", "    cv=5,\n", "    scoring='neg_mean_squared_error',\n", "    n_jobs=-1\n", ")\n", "# Use the scaled AND imputed training data (xtrain_scaled_imputed)\n", "ridge_gs.fit(xtrain_scaled_imputed, ytrain)\n", "best_ridge_alpha = ridge_gs.best_params_['alpha']\n", "best_ridge_solver = ridge_gs.best_params_['solver']\n", "print(f'GridSearchCV Ridge best alpha: {best_ridge_alpha:.5f}, solver: {best_ridge_solver}')\n", "\n", "# Use the scaled AND imputed test data (xtest_scaled_imputed) for prediction\n", "y_ridge_gs_pred = ridge_gs.predict(xtest_scaled_imputed)\n", "ridge_gs_r2 = r2_score(ytest, y_ridge_gs_pred)\n", "ridge_gs_rmse = np.sqrt(mean_squared_error(ytest, y_ridge_gs_pred))\n", "print(f'Ridge GridSearchCV → R²: {ridge_gs_r2:.4f}, RMSE: ${ridge_gs_rmse:,.0f}')"], "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "XJuWf8YiTOX0", "outputId": "a73711ae-14c3-47e2-c953-8b8cc5b4489c"}, "execution_count": 31, "outputs": [{"output_type": "stream", "name": "stdout", "text": ["GridSearchCV Ridge best alpha: 8.85867, solver: auto\n", "Ridge GridSearchCV → R²: 0.8604, RMSE: $28,169\n"]}]}, {"cell_type": "markdown", "source": ["### 15.3 ElasticNet GridSearchCV\n", "\n", "* We perform a grid search over both `alpha` and `l1_ratio` for `ElasticNet` to find the optimal combination."], "metadata": {"id": "3WXIrfdHfxzh"}}, {"cell_type": "code", "source": ["from sklearn.linear_model import ElasticNet # Import ElasticNet here\n", "from sklearn.model_selection import GridSearchCV # Import GridSearchCV if not already available\n", "\n", "enet_param_grid = {\n", "    'alpha': np.logspace(-4, 1, 20),  # from 1e-4 to 1e1\n", "    'l1_ratio': [0.1, 0.3, 0.5, 0.7, 0.9]\n", "}\n", "\n", "enet_gs = GridSearchCV(\n", "    ElasticNet(max_iter=15000, random_state=42),\n", "    param_grid=enet_param_grid,\n", "    cv=5,\n", "    scoring='neg_mean_squared_error',\n", "    n_jobs=-1\n", ")\n", "# Assuming xtrain_scaled_imputed and xtest_scaled_imputed are intended here\n", "# as used in other GridSearchCV sections (LassoGS, RidgeGS)\n", "enet_gs.fit(xtrain_scaled_imputed, ytrain)\n", "best_enet_alpha = enet_gs.best_params_['alpha']\n", "best_enet_l1r   = enet_gs.best_params_['l1_ratio']\n", "print(f'GridSearchCV ElasticNet best alpha: {best_enet_alpha:.5f}, l1_ratio: {best_enet_l1r}')\n", "\n", "# Assuming xtest_scaled_imputed is intended here\n", "y_enet_gs_pred = enet_gs.predict(xtest_scaled_imputed)\n", "enet_gs_r2 = r2_score(ytest, y_enet_gs_pred)\n", "enet_gs_rmse = np.sqrt(mean_squared_error(ytest, y_enet_gs_pred))\n", "print(f'ElasticNet GridSearchCV → R²: {enet_gs_r2:.4f}, RMSE: ${enet_gs_rmse:,.0f}')"], "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "mdFoYyfMTOjw", "outputId": "6616844d-b237-44a4-ca15-3c87a602e96a"}, "execution_count": 32, "outputs": [{"output_type": "stream", "name": "stdout", "text": ["GridSearchCV ElasticNet best alpha: 0.14384, l1_ratio: 0.9\n", "ElasticNet GridSearchCV → R²: 0.8655, RMSE: $27,651\n"]}]}, {"cell_type": "markdown", "source": ["### 15.4 RandomForestRegressor GridSearchCV\n", "\n", "* Introduce `RandomForestRegressor` and tune its key parameters: number of trees, max depth, and leaf criteria."], "metadata": {"id": "6VeI_Z_MgR0o"}}, {"cell_type": "code", "source": ["from sklearn.ensemble import RandomForestRegressor # Import RandomForestRegressor here\n", "from sklearn.model_selection import GridSearchCV # Import GridSearchCV if not already available\n", "import numpy as np # Import numpy if not already available from previous cells\n", "from sklearn.metrics import r2_score, mean_squared_error # Import metrics if not already available\n", "\n", "\n", "rf_param_grid = {\n", "    'n_estimators': [100, 200],\n", "    'max_depth': [None, 10, 20],\n", "    'min_samples_split': [2, 5],\n", "    'min_samples_leaf': [1, 2]\n", "}\n", "\n", "rf_gs = GridSearchCV(\n", "    RandomForestRegressor(random_state=42),\n", "    param_grid=rf_param_grid,\n", "    cv=3,\n", "    scoring='neg_mean_squared_error',\n", "    n_jobs=-1,\n", "    verbose=2\n", ")\n", "# Ensure xtrain_scaled and ytrain are correctly defined and available\n", "rf_gs.fit(xtrain_scaled, ytrain)\n", "best_rf_params = rf_gs.best_params_\n", "print('GridSearchCV RandomForest best parameters:', best_rf_params)\n", "\n", "# Ensure xtest_scaled and ytest are correctly defined and available\n", "y_rf_gs_pred = rf_gs.predict(xtest_scaled)\n", "rf_gs_r2 = r2_score(ytest, y_rf_gs_pred)\n", "rf_gs_rmse = np.sqrt(mean_squared_error(ytest, y_rf_gs_pred))\n", "print(f'RandomForest GridSearchCV → R²: {rf_gs_r2:.4f}, RMSE: ${rf_gs_rmse:,.0f}')"], "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "wUaJT_xwTPQw", "outputId": "3a253f6b-d2a9-4f78-da07-a84845fb676a"}, "execution_count": 33, "outputs": [{"output_type": "stream", "name": "stdout", "text": ["Fitting 3 folds for each of 24 candidates, totalling 72 fits\n", "GridSearchCV RandomForest best parameters: {'max_depth': 20, 'min_samples_leaf': 1, 'min_samples_split': 2, 'n_estimators': 200}\n", "RandomForest GridSearchCV → R²: 0.9034, RMSE: $23,426\n"]}]}, {"cell_type": "code", "source": ["updated_results = pd.DataFrame({\n", "    'Model': [\n", "        '<PERSON><PERSON> (Mean)',\n", "        'OLS LinearRegression',\n", "        'LassoCV',\n", "        '<PERSON>so GridSearchCV',\n", "        'RidgeCV',\n", "        'Ridge GridSearchCV',\n", "        'ElasticNetCV',\n", "        'ElasticNet GridSearchCV',\n", "        'RandomForest GridSearchCV'\n", "    ],\n", "    'Test R²': [\n", "        r2_score(ytest, y_base_pred),\n", "        lr_r2,\n", "        lasso_r2,\n", "        lasso_gs_r2,\n", "        ridge_r2,\n", "        ridge_gs_r2,\n", "        enet_r2,\n", "        enet_gs_r2,\n", "        rf_gs_r2\n", "    ],\n", "    'Test RMSE': [\n", "        baseline_rmse,\n", "        lr_rmse,\n", "        lasso_rmse,\n", "        lasso_gs_rmse,\n", "        ridge_rmse,\n", "        ridge_gs_rmse,\n", "        enet_rmse,\n", "        enet_gs_rmse,\n", "        rf_gs_rmse\n", "    ]\n", "}).set_index('Model')\n", "\n", "updated_results"], "metadata": {"colab": {"base_uri": "https://localhost:8080/", "height": 363}, "id": "Ma8R_5KrTQGD", "outputId": "53fe2ce4-d850-498d-f954-cb72b35b9ac4"}, "execution_count": 34, "outputs": [{"output_type": "execute_result", "data": {"text/plain": ["                            Test R²     Test RMSE\n", "Model                                            \n", "Baseline (Mean)           -0.006230  75622.018753\n", "OLS LinearRegression       0.842418  29926.263756\n", "LassoCV                    0.874416  26715.701815\n", "Lasso GridSearchCV         0.841281  30034.109254\n", "RidgeCV                    0.862840  27919.907755\n", "Ridge GridSearchCV         0.860380  28169.139728\n", "ElasticNetCV               0.874416  26715.701815\n", "ElasticNet GridSearchCV    0.865472  27650.661614\n", "RandomForest GridSearchCV  0.903442  23425.768141"], "text/html": ["\n", "  <div id=\"df-a74bef89-e574-48d4-ad78-b0980e1a32ab\" class=\"colab-df-container\">\n", "    <div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>Test R²</th>\n", "      <th>Test RMSE</th>\n", "    </tr>\n", "    <tr>\n", "      <th>Model</th>\n", "      <th></th>\n", "      <th></th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th><PERSON><PERSON> (Mean)</th>\n", "      <td>-0.006230</td>\n", "      <td>75622.018753</td>\n", "    </tr>\n", "    <tr>\n", "      <th>OLS LinearRegression</th>\n", "      <td>0.842418</td>\n", "      <td>29926.263756</td>\n", "    </tr>\n", "    <tr>\n", "      <th>LassoCV</th>\n", "      <td>0.874416</td>\n", "      <td>26715.701815</td>\n", "    </tr>\n", "    <tr>\n", "      <th>Lasso GridSearchCV</th>\n", "      <td>0.841281</td>\n", "      <td>30034.109254</td>\n", "    </tr>\n", "    <tr>\n", "      <th>RidgeCV</th>\n", "      <td>0.862840</td>\n", "      <td>27919.907755</td>\n", "    </tr>\n", "    <tr>\n", "      <th>Ridge GridSearchCV</th>\n", "      <td>0.860380</td>\n", "      <td>28169.139728</td>\n", "    </tr>\n", "    <tr>\n", "      <th>ElasticNetCV</th>\n", "      <td>0.874416</td>\n", "      <td>26715.701815</td>\n", "    </tr>\n", "    <tr>\n", "      <th>ElasticNet GridSearchCV</th>\n", "      <td>0.865472</td>\n", "      <td>27650.661614</td>\n", "    </tr>\n", "    <tr>\n", "      <th>RandomForest GridSearchCV</th>\n", "      <td>0.903442</td>\n", "      <td>23425.768141</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>\n", "    <div class=\"colab-df-buttons\">\n", "\n", "  <div class=\"colab-df-container\">\n", "    <button class=\"colab-df-convert\" onclick=\"convertToInteractive('df-a74bef89-e574-48d4-ad78-b0980e1a32ab')\"\n", "            title=\"Convert this dataframe to an interactive table.\"\n", "            style=\"display:none;\">\n", "\n", "  <svg xmlns=\"http://www.w3.org/2000/svg\" height=\"24px\" viewBox=\"0 -960 960 960\">\n", "    <path d=\"M120-120v-720h720v720H120Zm60-500h600v-160H180v160Zm220 220h160v-160H400v160Zm0 220h160v-160H400v160ZM180-400h160v-160H180v160Zm440 0h160v-160H620v160ZM180-180h160v-160H180v160Zm440 0h160v-160H620v160Z\"/>\n", "  </svg>\n", "    </button>\n", "\n", "  <style>\n", "    .colab-df-container {\n", "      display:flex;\n", "      gap: 12px;\n", "    }\n", "\n", "    .colab-df-convert {\n", "      background-color: #E8F0FE;\n", "      border: none;\n", "      border-radius: 50%;\n", "      cursor: pointer;\n", "      display: none;\n", "      fill: #1967D2;\n", "      height: 32px;\n", "      padding: 0 0 0 0;\n", "      width: 32px;\n", "    }\n", "\n", "    .colab-df-convert:hover {\n", "      background-color: #E2EBFA;\n", "      box-shadow: 0px 1px 2px rgba(60, 64, 67, 0.3), 0px 1px 3px 1px rgba(60, 64, 67, 0.15);\n", "      fill: #174EA6;\n", "    }\n", "\n", "    .colab-df-buttons div {\n", "      margin-bottom: 4px;\n", "    }\n", "\n", "    [theme=dark] .colab-df-convert {\n", "      background-color: #3B4455;\n", "      fill: #D2E3FC;\n", "    }\n", "\n", "    [theme=dark] .colab-df-convert:hover {\n", "      background-color: #434B5C;\n", "      box-shadow: 0px 1px 3px 1px rgba(0, 0, 0, 0.15);\n", "      filter: drop-shadow(0px 1px 2px rgba(0, 0, 0, 0.3));\n", "      fill: #FFFFFF;\n", "    }\n", "  </style>\n", "\n", "    <script>\n", "      const buttonEl =\n", "        document.querySelector('#df-a74bef89-e574-48d4-ad78-b0980e1a32ab button.colab-df-convert');\n", "      buttonEl.style.display =\n", "        google.colab.kernel.accessAllowed ? 'block' : 'none';\n", "\n", "      async function convertToInteractive(key) {\n", "        const element = document.querySelector('#df-a74bef89-e574-48d4-ad78-b0980e1a32ab');\n", "        const dataTable =\n", "          await google.colab.kernel.invokeFunction('convertToInteractive',\n", "                                                    [key], {});\n", "        if (!dataTable) return;\n", "\n", "        const docLinkHtml = 'Like what you see? Visit the ' +\n", "          '<a target=\"_blank\" href=https://colab.research.google.com/notebooks/data_table.ipynb>data table notebook</a>'\n", "          + ' to learn more about interactive tables.';\n", "        element.innerHTML = '';\n", "        dataTable['output_type'] = 'display_data';\n", "        await google.colab.output.renderOutput(dataTable, element);\n", "        const docLink = document.createElement('div');\n", "        docLink.innerHTML = docLinkHtml;\n", "        element.appendChild(docLink);\n", "      }\n", "    </script>\n", "  </div>\n", "\n", "\n", "    <div id=\"df-021ccf38-e78f-47a8-b2e8-d0f67ba33f9e\">\n", "      <button class=\"colab-df-quickchart\" onclick=\"quickchart('df-021ccf38-e78f-47a8-b2e8-d0f67ba33f9e')\"\n", "                title=\"Suggest charts\"\n", "                style=\"display:none;\">\n", "\n", "<svg xmlns=\"http://www.w3.org/2000/svg\" height=\"24px\"viewBox=\"0 0 24 24\"\n", "     width=\"24px\">\n", "    <g>\n", "        <path d=\"M19 3H5c-1.1 0-2 .9-2 2v14c0 1.1.9 2 2 2h14c1.1 0 2-.9 2-2V5c0-1.1-.9-2-2-2zM9 17H7v-7h2v7zm4 0h-2V7h2v10zm4 0h-2v-4h2v4z\"/>\n", "    </g>\n", "</svg>\n", "      </button>\n", "\n", "<style>\n", "  .colab-df-quickchart {\n", "      --bg-color: #E8F0FE;\n", "      --fill-color: #1967D2;\n", "      --hover-bg-color: #E2EBFA;\n", "      --hover-fill-color: #174EA6;\n", "      --disabled-fill-color: #AAA;\n", "      --disabled-bg-color: #DDD;\n", "  }\n", "\n", "  [theme=dark] .colab-df-quickchart {\n", "      --bg-color: #3B4455;\n", "      --fill-color: #D2E3FC;\n", "      --hover-bg-color: #434B5C;\n", "      --hover-fill-color: #FFFFFF;\n", "      --disabled-bg-color: #3B4455;\n", "      --disabled-fill-color: #666;\n", "  }\n", "\n", "  .colab-df-quickchart {\n", "    background-color: var(--bg-color);\n", "    border: none;\n", "    border-radius: 50%;\n", "    cursor: pointer;\n", "    display: none;\n", "    fill: var(--fill-color);\n", "    height: 32px;\n", "    padding: 0;\n", "    width: 32px;\n", "  }\n", "\n", "  .colab-df-quickchart:hover {\n", "    background-color: var(--hover-bg-color);\n", "    box-shadow: 0 1px 2px rgba(60, 64, 67, 0.3), 0 1px 3px 1px rgba(60, 64, 67, 0.15);\n", "    fill: var(--button-hover-fill-color);\n", "  }\n", "\n", "  .colab-df-quickchart-complete:disabled,\n", "  .colab-df-quickchart-complete:disabled:hover {\n", "    background-color: var(--disabled-bg-color);\n", "    fill: var(--disabled-fill-color);\n", "    box-shadow: none;\n", "  }\n", "\n", "  .colab-df-spinner {\n", "    border: 2px solid var(--fill-color);\n", "    border-color: transparent;\n", "    border-bottom-color: var(--fill-color);\n", "    animation:\n", "      spin 1s steps(1) infinite;\n", "  }\n", "\n", "  @keyframes spin {\n", "    0% {\n", "      border-color: transparent;\n", "      border-bottom-color: var(--fill-color);\n", "      border-left-color: var(--fill-color);\n", "    }\n", "    20% {\n", "      border-color: transparent;\n", "      border-left-color: var(--fill-color);\n", "      border-top-color: var(--fill-color);\n", "    }\n", "    30% {\n", "      border-color: transparent;\n", "      border-left-color: var(--fill-color);\n", "      border-top-color: var(--fill-color);\n", "      border-right-color: var(--fill-color);\n", "    }\n", "    40% {\n", "      border-color: transparent;\n", "      border-right-color: var(--fill-color);\n", "      border-top-color: var(--fill-color);\n", "    }\n", "    60% {\n", "      border-color: transparent;\n", "      border-right-color: var(--fill-color);\n", "    }\n", "    80% {\n", "      border-color: transparent;\n", "      border-right-color: var(--fill-color);\n", "      border-bottom-color: var(--fill-color);\n", "    }\n", "    90% {\n", "      border-color: transparent;\n", "      border-bottom-color: var(--fill-color);\n", "    }\n", "  }\n", "</style>\n", "\n", "      <script>\n", "        async function quickchart(key) {\n", "          const quickchartButtonEl =\n", "            document.querySelector('#' + key + ' button');\n", "          quickchartButtonEl.disabled = true;  // To prevent multiple clicks.\n", "          quickchartButtonEl.classList.add('colab-df-spinner');\n", "          try {\n", "            const charts = await google.colab.kernel.invokeFunction(\n", "                'suggest<PERSON><PERSON>s', [key], {});\n", "          } catch (error) {\n", "            console.error('Error during call to suggest<PERSON>harts:', error);\n", "          }\n", "          quickchartButtonEl.classList.remove('colab-df-spinner');\n", "          quickchartButtonEl.classList.add('colab-df-quickchart-complete');\n", "        }\n", "        (() => {\n", "          let quickchartButtonEl =\n", "            document.querySelector('#df-021ccf38-e78f-47a8-b2e8-d0f67ba33f9e button');\n", "          quickchartButtonEl.style.display =\n", "            google.colab.kernel.accessAllowed ? 'block' : 'none';\n", "        })();\n", "      </script>\n", "    </div>\n", "\n", "  <div id=\"id_7f866cb8-e398-4e1c-9a46-e15d88820d6f\">\n", "    <style>\n", "      .colab-df-generate {\n", "        background-color: #E8F0FE;\n", "        border: none;\n", "        border-radius: 50%;\n", "        cursor: pointer;\n", "        display: none;\n", "        fill: #1967D2;\n", "        height: 32px;\n", "        padding: 0 0 0 0;\n", "        width: 32px;\n", "      }\n", "\n", "      .colab-df-generate:hover {\n", "        background-color: #E2EBFA;\n", "        box-shadow: 0px 1px 2px rgba(60, 64, 67, 0.3), 0px 1px 3px 1px rgba(60, 64, 67, 0.15);\n", "        fill: #174EA6;\n", "      }\n", "\n", "      [theme=dark] .colab-df-generate {\n", "        background-color: #3B4455;\n", "        fill: #D2E3FC;\n", "      }\n", "\n", "      [theme=dark] .colab-df-generate:hover {\n", "        background-color: #434B5C;\n", "        box-shadow: 0px 1px 3px 1px rgba(0, 0, 0, 0.15);\n", "        filter: drop-shadow(0px 1px 2px rgba(0, 0, 0, 0.3));\n", "        fill: #FFFFFF;\n", "      }\n", "    </style>\n", "    <button class=\"colab-df-generate\" onclick=\"generateWithVariable('updated_results')\"\n", "            title=\"Generate code using this dataframe.\"\n", "            style=\"display:none;\">\n", "\n", "  <svg xmlns=\"http://www.w3.org/2000/svg\" height=\"24px\"viewBox=\"0 0 24 24\"\n", "       width=\"24px\">\n", "    <path d=\"M7,19H8.4L18.45,9,17,7.55,7,17.6ZM5,21V16.75L18.45,3.32a2,2,0,0,1,2.83,0l1.4,1.43a1.91,1.91,0,0,1,.58,1.4,1.91,1.91,0,0,1-.58,1.4L9.25,21ZM18.45,9,17,7.55Zm-12,3A5.31,5.31,0,0,0,4.9,8.1,5.31,5.31,0,0,0,1,6.5,5.31,5.31,0,0,0,4.9,4.9,5.31,5.31,0,0,0,6.5,1,5.31,5.31,0,0,0,8.1,4.9,5.31,5.31,0,0,0,12,6.5,5.46,5.46,0,0,0,6.5,12Z\"/>\n", "  </svg>\n", "    </button>\n", "    <script>\n", "      (() => {\n", "      const buttonEl =\n", "        document.querySelector('#id_7f866cb8-e398-4e1c-9a46-e15d88820d6f button.colab-df-generate');\n", "      buttonEl.style.display =\n", "        google.colab.kernel.accessAllowed ? 'block' : 'none';\n", "\n", "      buttonEl.onclick = () => {\n", "        google.colab.notebook.generateWithVariable('updated_results');\n", "      }\n", "      })();\n", "    </script>\n", "  </div>\n", "\n", "    </div>\n", "  </div>\n"], "application/vnd.google.colaboratory.intrinsic+json": {"type": "dataframe", "variable_name": "updated_results", "summary": "{\n  \"name\": \"updated_results\",\n  \"rows\": 9,\n  \"fields\": [\n    {\n      \"column\": \"Model\",\n      \"properties\": {\n        \"dtype\": \"string\",\n        \"num_unique_values\": 9,\n        \"samples\": [\n          \"ElasticNet GridSearchCV\",\n          \"OLS LinearRegression\",\n          \"Ridge GridSearchCV\"\n        ],\n        \"semantic_type\": \"\",\n        \"description\": \"\"\n      }\n    },\n    {\n      \"column\": \"Test R\\u00b2\",\n      \"properties\": {\n        \"dtype\": \"number\",\n        \"std\": 0.29119545883396364,\n        \"min\": -0.006229996368665169,\n        \"max\": 0.9034419676480006,\n        \"num_unique_values\": 8,\n        \"samples\": [\n          0.8424183205391346,\n          0.8603799162242614,\n          -0.006229996368665169\n        ],\n        \"semantic_type\": \"\",\n        \"description\": \"\"\n      }\n    },\n    {\n      \"column\": \"Test RMSE\",\n      \"properties\": {\n        \"dtype\": \"number\",\n        \"std\": 16136.978859293591,\n        \"min\": 23425.768141344553,\n        \"max\": 75622.01875276642,\n        \"num_unique_values\": 8,\n        \"samples\": [\n          29926.26375642111,\n          28169.139727930837,\n          75622.01875276642\n        ],\n        \"semantic_type\": \"\",\n        \"description\": \"\"\n      }\n    }\n  ]\n}"}}, "metadata": {}, "execution_count": 34}]}, {"cell_type": "markdown", "source": ["## 17. Hyperparameter Tuning & MLP Classifier\n", "\n", "In this final section, we demonstrate how to perform **train/test evaluation** and **GridSearchCV hyperparameter tuning** for a classification problem, then fit an **MLPClassifier (ANN)** and show its **training & testing accuracy** along with a **confusion matrix**. The steps are:\n", "\n", "1. Load a classification dataset (e.g., employee‐performance or any other CSV).\n", "2. Split into train/test.\n", "3. Fit a baseline classifier (RandomForest) → report train/test accuracy.\n", "4. <PERSON>ne that classifier with `GridSearchCV` → report train/test accuracy.\n", "5. Fit an MLPClassifier (ANN) → report train/test accuracy + confusion matrix.\n", "\n", "You can replace `classification_data.csv` and `target_column` with your own dataset and label."], "metadata": {"id": "yGq7XNT14MY5"}}, {"cell_type": "code", "source": ["# 17.1 Imports (for classification)\n", "import pandas as pd\n", "import numpy as np\n", "import matplotlib.pyplot as plt\n", "import seaborn as sns\n", "\n", "from sklearn.model_selection  import train_test_split, GridSearchCV\n", "from sklearn.metrics         import accuracy_score, confusion_matrix, ConfusionMatrixDisplay, classification_report\n", "from sklearn.ensemble        import RandomForestClassifier\n", "from sklearn.neural_network  import MLPClassifier\n"], "metadata": {"id": "AoeQrfIPTQTR"}, "execution_count": 35, "outputs": []}, {"cell_type": "markdown", "source": ["# 17.2 Load & Split Classification Dataset"], "metadata": {"id": "_WMX-9Po6UeT"}}, {"cell_type": "code", "source": [], "metadata": {"id": "B-7YdqE9QOsq"}, "execution_count": 35, "outputs": []}, {"cell_type": "code", "source": ["# 17.2 Load & Split Classification Dataset\n", "df_clf = pd.read_csv(\"/content/house_price_preprocessed_data_final.csv\")  # Replace with your CSV path\n", "print(\"Classification data shape:\", df_clf.shape)\n", "df_clf.head()\n", "\n", "# Using 'saleprice' as the target variable (Note: This is typically for regression)\n", "# Ensure 'saleprice' is treated as the target here\n", "# IMPORTANT: Classification models expect a categorical target.\n", "# Using 'saleprice' as a continuous target for classification might lead to unexpected results.\n", "y_clf = df_clf[\"saleprice\"]  # Using 'saleprice' as the target\n", "X_clf = df_clf.drop(\"saleprice\", axis=1) # Dropping 'saleprice' from features\n", "\n", "# Note: Stratification with a continuous target like 'saleprice' might not be appropriate.\n", "# If you proceed with this, be aware of the limitations.\n", "# For a true classification problem, you'd need to discretize 'saleprice' or use a different categorical column.\n", "\n", "# Splitting the data\n", "X_train_clf, X_test_clf, y_train_clf, y_test_clf = train_test_split(\n", "    X_clf, y_clf, test_size=0.20, random_state=42 # Removed stratify as it's for classification\n", ")\n", "\n", "print(\"Training set:\", X_train_clf.shape, y_train_clf.shape)\n", "print(\"Test set:    \", X_test_clf.shape, y_test_clf.shape)"], "metadata": {"id": "NoZOCdxeTQgW", "colab": {"base_uri": "https://localhost:8080/"}, "outputId": "2e550377-9d90-4c4c-9b5a-1615256c60ae"}, "execution_count": 36, "outputs": [{"output_type": "stream", "name": "stdout", "text": ["Classification data shape: (1197, 92)\n", "Training set: (957, 91) (957,)\n", "Test set:     (240, 91) (240,)\n"]}]}, {"cell_type": "markdown", "source": ["# 17.3 Load & Split Classification Dataset"], "metadata": {"id": "MySy1pxx6X69"}}, {"cell_type": "code", "source": ["# 17.3 Load & Split Classification Dataset\n", "\n", "# Reusing the already one-hot encoded dataframe from the regression section\n", "# This avoids the need to re-encode categorical variables\n", "df_clf = df_encoded.copy() # Use the already encoded dataframe\n", "\n", "print(\"Classification data shape from encoded dataframe:\", df_clf.shape)\n", "# df_clf.head() # Optional: Check the head, it should show many numeric columns\n", "\n", "y_clf = df_clf[\"saleprice\"]  # Using 'saleprice' as the target\n", "X_clf = df_clf.drop(\"saleprice\", axis=1) # Drop the target column from features\n", "# --- END FIX ---\n", "\n", "\n", "# IMPORTANT: Impute missing values in the classification feature matrix X_clf\n", "# before splitting or training, similar to the regression process.\n", "# Create an imputer object\n", "imputer_clf = SimpleImputer(strategy='mean')\n", "\n", "# Fit and transform the features\n", "X_clf_imputed = imputer_clf.fit_transform(X_clf)\n", "\n", "# Stratification is used for classification problems with categorical targets\n", "X_train_clf, X_test_clf, y_train_clf, y_test_clf = train_test_split(\n", "    X_clf_imputed, y_clf, test_size=0.20, random_state=42 # Removed stratify\n", ")\n", "\n", "\n", "print(\"Training set (imputed):\", X_train_clf.shape, y_train_clf.shape)\n", "print(\"Test set    (imputed):\", X_test_clf.shape, y_test_clf.shape)"], "metadata": {"id": "doGCV6M4TQtH", "colab": {"base_uri": "https://localhost:8080/"}, "outputId": "09f99273-0fee-4650-84fd-6c941419ddbb"}, "execution_count": 37, "outputs": [{"output_type": "stream", "name": "stdout", "text": ["Classification data shape from encoded dataframe: (1197, 253)\n", "Training set (imputed): (957, 252) (957,)\n", "Test set    (imputed): (240, 252) (240,)\n"]}]}, {"cell_type": "markdown", "source": ["# 17.4 RandomForest Hyperparameter Tuning: GridSearchCV"], "metadata": {"id": "u39LF_756lM9"}}, {"cell_type": "code", "source": ["# 17.4 RandomForest Hyperparameter Tuning: GridSearchCV\n", "param_grid_rf = {\n", "    \"n_estimators\": [50, 100, 200],\n", "    \"max_depth\": [None, 20, 30],\n", "    \"min_samples_split\": [3, 7],\n", "    \"min_samples_leaf\": [2, 4]\n", "}\n", "\n", "grid_rf = GridSearchCV(\n", "    RandomForestClassifier(random_state=42),\n", "    param_grid=param_grid_rf,\n", "    cv=5,\n", "    scoring=\"accuracy\",\n", "    n_jobs=-1,\n", "    verbose=1\n", ")\n", "\n", "grid_rf.fit(X_train_clf, y_train_clf)\n", "\n", "print(\"Best RF params found by GridSearchCV:\")\n", "print(grid_rf.best_params_)\n", "print(f\"Best CV accuracy: {grid_rf.best_score_ * 100:.2f}%\\n\")\n", "\n", "# Extract best estimator\n", "rf_tuned = grid_rf.best_estimator_\n", "\n", "# Recompute train & test accuracy on the tuned model\n", "y_train_pred_tuned = rf_tuned.predict(X_train_clf)\n", "y_test_pred_tuned  = rf_tuned.predict(X_test_clf)\n", "\n", "train_acc_tuned = accuracy_score(y_train_clf, y_train_pred_tuned)\n", "test_acc_tuned  = accuracy_score(y_test_clf, y_test_pred_tuned)\n", "\n", "print(f\"RandomForest (TUNED) → Train accuracy: {train_acc_tuned * 100:.2f}%\")\n", "print(f\"RandomForest (TUNED) →  Test accuracy: {test_acc_tuned  * 100:.2f}%\")\n"], "metadata": {"id": "fEKAKAn1TQ6O", "colab": {"base_uri": "https://localhost:8080/"}, "outputId": "edc80c97-8e40-4b78-a50b-98eaca4cdf07"}, "execution_count": 38, "outputs": [{"output_type": "stream", "name": "stdout", "text": ["Fitting 5 folds for each of 36 candidates, totalling 180 fits\n"]}, {"output_type": "stream", "name": "stderr", "text": ["/usr/local/lib/python3.11/dist-packages/sklearn/model_selection/_split.py:805: UserWarning: The least populated class in y has only 1 members, which is less than n_splits=5.\n", "  warnings.warn(\n"]}, {"output_type": "stream", "name": "stdout", "text": ["Best RF params found by GridSearchCV:\n", "{'max_depth': None, 'min_samples_leaf': 2, 'min_samples_split': 7, 'n_estimators': 50}\n", "Best CV accuracy: 2.51%\n", "\n", "RandomForest (TUNED) → Train accuracy: 99.27%\n", "RandomForest (TUNED) →  Test accuracy: 1.67%\n"]}]}, {"cell_type": "markdown", "source": ["# Artificial Neural Network: MLPClassifier with Hyperparameter Tuning"], "metadata": {"id": "Z1AyuSYgz9zl"}}, {"cell_type": "code", "source": ["# 17.5 Artificial Neural Network: MLPClassifier with Hyperparameter Tuning\n", "\n", "from sklearn.model_selection import GridSearchCV\n", "from sklearn.neural_network import MLPClassifier\n", "\n", "# Define hyperparameter grid for MLPClassifier\n", "param_grid_mlp = {\n", "    'hidden_layer_sizes': [(50,), (100,), (100, 50), (50, 25, 10)],\n", "    'alpha': [1e-4, 1e-3, 1e-2],\n", "    'learning_rate_init': [0.001, 0.01],\n", "    'activation': ['relu', 'tanh'],\n", "    'solver': ['adam']\n", "}\n", "\n", "# Instantiate base MLPClassifier\n", "mlp_base = MLPClassifier(max_iter=1000, random_state=42)\n", "\n", "# GridSearchCV for MLP\n", "grid_mlp = GridSearchCV(\n", "    mlp_base,\n", "    param_grid=param_grid_mlp,\n", "    cv=5,\n", "    scoring='accuracy',\n", "    n_jobs=-1,\n", "    verbose=1\n", ")\n", "grid_mlp.fit(X_train_clf, y_train_clf)\n", "\n", "# Best parameters and CV score\n", "print(\"Best MLP params found by GridSearchCV:\")\n", "print(grid_mlp.best_params_)\n", "print(f\"Best CV accuracy: {grid_mlp.best_score_ * 100:.2f}%\\n\")\n", "\n", "# Extract best estimator\n", "mlp_best = grid_mlp.best_estimator_\n", "\n", "# Compute train & test accuracy\n", "y_train_pred_mlp = mlp_best.predict(X_train_clf)\n", "y_test_pred_mlp  = mlp_best.predict(X_test_clf)\n", "\n", "train_acc_mlp = accuracy_score(y_train_clf, y_train_pred_mlp)\n", "test_acc_mlp  = accuracy_score(y_test_clf, y_test_pred_mlp)\n", "\n", "print(f\"MLPClassifier (TUNED) → Train accuracy: {train_acc_mlp * 100:.2f}%\")\n", "print(f\"MLPClassifier (TUNED) →  Test accuracy: {test_acc_mlp  * 100:.2f}%\\n\")\n", "\n", "# Confusion Matrix (Test set)\n", "cm = confusion_matrix(y_test_clf, y_test_pred_mlp)\n", "disp = ConfusionMatrixDisplay(confusion_matrix=cm, display_labels=mlp_best.classes_)\n", "plt.figure(figsize=(6, 6))\n", "disp.plot(cmap=\"Blues\", values_format=\"d\")\n", "plt.title(\"MLPClassifier (TUNED): Confusion Matrix (Test Set)\")\n", "plt.show()\n", "\n", "# Detailed classification report\n", "print(\"Classification Report (Test Set):\")\n", "print(classification_report(y_test_clf, y_test_pred_mlp))"], "metadata": {"id": "JYEwIf_STRMb", "colab": {"base_uri": "https://localhost:8080/", "height": 966}, "outputId": "8e80d43d-ae01-427b-b396-5c90a15b8b3b"}, "execution_count": 39, "outputs": [{"output_type": "stream", "name": "stdout", "text": ["Fitting 5 folds for each of 48 candidates, totalling 240 fits\n"]}, {"output_type": "stream", "name": "stderr", "text": ["/usr/local/lib/python3.11/dist-packages/sklearn/model_selection/_split.py:805: UserWarning: The least populated class in y has only 1 members, which is less than n_splits=5.\n", "  warnings.warn(\n"]}, {"output_type": "stream", "name": "stdout", "text": ["Best MLP params found by GridSearchCV:\n", "{'activation': 'tanh', 'alpha': 0.01, 'hidden_layer_sizes': (50, 25, 10), 'learning_rate_init': 0.001, 'solver': 'adam'}\n", "Best CV accuracy: 1.88%\n", "\n", "MLPClassifier (TUNED) → Train accuracy: 2.30%\n", "MLPClassifier (TUNED) →  Test accuracy: 0.83%\n", "\n"]}, {"output_type": "error", "ename": "ValueError", "evalue": "The number of FixedLocator locations (193), usually from a call to set_ticks, does not match the number of labels (517).", "traceback": ["\u001b[0;31m---------------------------------------------------------------------------\u001b[0m", "\u001b[0;31m<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>\u001b[0m                                <PERSON><PERSON> (most recent call last)", "\u001b[0;32m<ipython-input-39-76198e58ecca>\u001b[0m in \u001b[0;36m<cell line: 0>\u001b[0;34m()\u001b[0m\n\u001b[1;32m     49\u001b[0m \u001b[0mdisp\u001b[0m \u001b[0;34m=\u001b[0m \u001b[0mConfusionMatrixDisplay\u001b[0m\u001b[0;34m(\u001b[0m\u001b[0mconfusion_matrix\u001b[0m\u001b[0;34m=\u001b[0m\u001b[0mcm\u001b[0m\u001b[0;34m,\u001b[0m \u001b[0mdisplay_labels\u001b[0m\u001b[0;34m=\u001b[0m\u001b[0mmlp_best\u001b[0m\u001b[0;34m.\u001b[0m\u001b[0mclasses_\u001b[0m\u001b[0;34m)\u001b[0m\u001b[0;34m\u001b[0m\u001b[0;34m\u001b[0m\u001b[0m\n\u001b[1;32m     50\u001b[0m \u001b[0mplt\u001b[0m\u001b[0;34m.\u001b[0m\u001b[0mfigure\u001b[0m\u001b[0;34m(\u001b[0m\u001b[0mfigsize\u001b[0m\u001b[0;34m=\u001b[0m\u001b[0;34m(\u001b[0m\u001b[0;36m6\u001b[0m\u001b[0;34m,\u001b[0m \u001b[0;36m6\u001b[0m\u001b[0;34m)\u001b[0m\u001b[0;34m)\u001b[0m\u001b[0;34m\u001b[0m\u001b[0;34m\u001b[0m\u001b[0m\n\u001b[0;32m---> 51\u001b[0;31m \u001b[0mdisp\u001b[0m\u001b[0;34m.\u001b[0m\u001b[0mplot\u001b[0m\u001b[0;34m(\u001b[0m\u001b[0mcmap\u001b[0m\u001b[0;34m=\u001b[0m\u001b[0;34m\"Blues\"\u001b[0m\u001b[0;34m,\u001b[0m \u001b[0mvalues_format\u001b[0m\u001b[0;34m=\u001b[0m\u001b[0;34m\"d\"\u001b[0m\u001b[0;34m)\u001b[0m\u001b[0;34m\u001b[0m\u001b[0;34m\u001b[0m\u001b[0m\n\u001b[0m\u001b[1;32m     52\u001b[0m \u001b[0mplt\u001b[0m\u001b[0;34m.\u001b[0m\u001b[0mtitle\u001b[0m\u001b[0;34m(\u001b[0m\u001b[0;34m\"MLPClassifier (TUNED): Confusion Matrix (Test Set)\"\u001b[0m\u001b[0;34m)\u001b[0m\u001b[0;34m\u001b[0m\u001b[0;34m\u001b[0m\u001b[0m\n\u001b[1;32m     53\u001b[0m \u001b[0mplt\u001b[0m\u001b[0;34m.\u001b[0m\u001b[0mshow\u001b[0m\u001b[0;34m(\u001b[0m\u001b[0;34m)\u001b[0m\u001b[0;34m\u001b[0m\u001b[0;34m\u001b[0m\u001b[0m\n", "\u001b[0;32m/usr/local/lib/python3.11/dist-packages/sklearn/metrics/_plot/confusion_matrix.py\u001b[0m in \u001b[0;36mplot\u001b[0;34m(self, include_values, cmap, xticks_rotation, values_format, ax, colorbar, im_kw, text_kw)\u001b[0m\n\u001b[1;32m    183\u001b[0m         \u001b[0;32mif\u001b[0m \u001b[0mcolorbar\u001b[0m\u001b[0;34m:\u001b[0m\u001b[0;34m\u001b[0m\u001b[0;34m\u001b[0m\u001b[0m\n\u001b[1;32m    184\u001b[0m             \u001b[0mfig\u001b[0m\u001b[0;34m.\u001b[0m\u001b[0mcolorbar\u001b[0m\u001b[0;34m(\u001b[0m\u001b[0mself\u001b[0m\u001b[0;34m.\u001b[0m\u001b[0mim_\u001b[0m\u001b[0;34m,\u001b[0m \u001b[0max\u001b[0m\u001b[0;34m=\u001b[0m\u001b[0max\u001b[0m\u001b[0;34m)\u001b[0m\u001b[0;34m\u001b[0m\u001b[0;34m\u001b[0m\u001b[0m\n\u001b[0;32m--> 185\u001b[0;31m         ax.set(\n\u001b[0m\u001b[1;32m    186\u001b[0m             \u001b[0mxticks\u001b[0m\u001b[0;34m=\u001b[0m\u001b[0mnp\u001b[0m\u001b[0;34m.\u001b[0m\u001b[0marange\u001b[0m\u001b[0;34m(\u001b[0m\u001b[0mn_classes\u001b[0m\u001b[0;34m)\u001b[0m\u001b[0;34m,\u001b[0m\u001b[0;34m\u001b[0m\u001b[0;34m\u001b[0m\u001b[0m\n\u001b[1;32m    187\u001b[0m             \u001b[0myticks\u001b[0m\u001b[0;34m=\u001b[0m\u001b[0mnp\u001b[0m\u001b[0;34m.\u001b[0m\u001b[0marange\u001b[0m\u001b[0;34m(\u001b[0m\u001b[0mn_classes\u001b[0m\u001b[0;34m)\u001b[0m\u001b[0;34m,\u001b[0m\u001b[0;34m\u001b[0m\u001b[0;34m\u001b[0m\u001b[0m\n", "\u001b[0;32m/usr/local/lib/python3.11/dist-packages/matplotlib/artist.py\u001b[0m in \u001b[0;36m<lambda>\u001b[0;34m(self, **kwargs)\u001b[0m\n\u001b[1;32m    144\u001b[0m             \u001b[0;32mreturn\u001b[0m\u001b[0;34m\u001b[0m\u001b[0;34m\u001b[0m\u001b[0m\n\u001b[1;32m    145\u001b[0m \u001b[0;34m\u001b[0m\u001b[0m\n\u001b[0;32m--> 146\u001b[0;31m         \u001b[0mcls\u001b[0m\u001b[0;34m.\u001b[0m\u001b[0mset\u001b[0m \u001b[0;34m=\u001b[0m \u001b[0;32mlambda\u001b[0m \u001b[0mself\u001b[0m\u001b[0;34m,\u001b[0m \u001b[0;34m**\u001b[0m\u001b[0mkwargs\u001b[0m\u001b[0;34m:\u001b[0m \u001b[0mArtist\u001b[0m\u001b[0;34m.\u001b[0m\u001b[0mset\u001b[0m\u001b[0;34m(\u001b[0m\u001b[0mself\u001b[0m\u001b[0;34m,\u001b[0m \u001b[0;34m**\u001b[0m\u001b[0mkwargs\u001b[0m\u001b[0;34m)\u001b[0m\u001b[0;34m\u001b[0m\u001b[0;34m\u001b[0m\u001b[0m\n\u001b[0m\u001b[1;32m    147\u001b[0m         \u001b[0mcls\u001b[0m\u001b[0;34m.\u001b[0m\u001b[0mset\u001b[0m\u001b[0;34m.\u001b[0m\u001b[0m__name__\u001b[0m \u001b[0;34m=\u001b[0m \u001b[0;34m\"set\"\u001b[0m\u001b[0;34m\u001b[0m\u001b[0;34m\u001b[0m\u001b[0m\n\u001b[1;32m    148\u001b[0m         \u001b[0mcls\u001b[0m\u001b[0;34m.\u001b[0m\u001b[0mset\u001b[0m\u001b[0;34m.\u001b[0m\u001b[0m__qualname__\u001b[0m \u001b[0;34m=\u001b[0m \u001b[0;34mf\"{cls.__qualname__}.set\"\u001b[0m\u001b[0;34m\u001b[0m\u001b[0;34m\u001b[0m\u001b[0m\n", "\u001b[0;32m/usr/local/lib/python3.11/dist-packages/matplotlib/artist.py\u001b[0m in \u001b[0;36mset\u001b[0;34m(self, **kwargs)\u001b[0m\n\u001b[1;32m   1239\u001b[0m         \u001b[0;31m# Artist._update_set_signature_and_docstring() at the end of the\u001b[0m\u001b[0;34m\u001b[0m\u001b[0;34m\u001b[0m\u001b[0m\n\u001b[1;32m   1240\u001b[0m         \u001b[0;31m# module.\u001b[0m\u001b[0;34m\u001b[0m\u001b[0;34m\u001b[0m\u001b[0m\n\u001b[0;32m-> 1241\u001b[0;31m         \u001b[0;32mreturn\u001b[0m \u001b[0mself\u001b[0m\u001b[0;34m.\u001b[0m\u001b[0m_internal_update\u001b[0m\u001b[0;34m(\u001b[0m\u001b[0mcbook\u001b[0m\u001b[0;34m.\u001b[0m\u001b[0mnormalize_kwargs\u001b[0m\u001b[0;34m(\u001b[0m\u001b[0mkwargs\u001b[0m\u001b[0;34m,\u001b[0m \u001b[0mself\u001b[0m\u001b[0;34m)\u001b[0m\u001b[0;34m)\u001b[0m\u001b[0;34m\u001b[0m\u001b[0;34m\u001b[0m\u001b[0m\n\u001b[0m\u001b[1;32m   1242\u001b[0m \u001b[0;34m\u001b[0m\u001b[0m\n\u001b[1;32m   1243\u001b[0m     \u001b[0;34m@\u001b[0m\u001b[0mcontextlib\u001b[0m\u001b[0;34m.\u001b[0m\u001b[0mcontextmanager\u001b[0m\u001b[0;34m\u001b[0m\u001b[0;34m\u001b[0m\u001b[0m\n", "\u001b[0;32m/usr/local/lib/python3.11/dist-packages/matplotlib/artist.py\u001b[0m in \u001b[0;36m_internal_update\u001b[0;34m(self, kwargs)\u001b[0m\n\u001b[1;32m   1231\u001b[0m         \u001b[0mThe\u001b[0m \u001b[0mlack\u001b[0m \u001b[0mof\u001b[0m \u001b[0mprenormalization\u001b[0m \u001b[0;32mis\u001b[0m \u001b[0mto\u001b[0m \u001b[0mmaintain\u001b[0m \u001b[0mbackcompatibility\u001b[0m\u001b[0;34m.\u001b[0m\u001b[0;34m\u001b[0m\u001b[0;34m\u001b[0m\u001b[0m\n\u001b[1;32m   1232\u001b[0m         \"\"\"\n\u001b[0;32m-> 1233\u001b[0;31m         return self._update_props(\n\u001b[0m\u001b[1;32m   1234\u001b[0m             \u001b[0mkwargs\u001b[0m\u001b[0;34m,\u001b[0m \u001b[0;34m\"{cls.__name__}.set() got an unexpected keyword argument \"\u001b[0m\u001b[0;34m\u001b[0m\u001b[0;34m\u001b[0m\u001b[0m\n\u001b[1;32m   1235\u001b[0m             \"{prop_name!r}\")\n", "\u001b[0;32m/usr/local/lib/python3.11/dist-packages/matplotlib/artist.py\u001b[0m in \u001b[0;36m_update_props\u001b[0;34m(self, props, errfmt)\u001b[0m\n\u001b[1;32m   1207\u001b[0m                             \u001b[0merrfmt\u001b[0m\u001b[0;34m.\u001b[0m\u001b[0mformat\u001b[0m\u001b[0;34m(\u001b[0m\u001b[0mcls\u001b[0m\u001b[0;34m=\u001b[0m\u001b[0mtype\u001b[0m\u001b[0;34m(\u001b[0m\u001b[0mself\u001b[0m\u001b[0;34m)\u001b[0m\u001b[0;34m,\u001b[0m \u001b[0mprop_name\u001b[0m\u001b[0;34m=\u001b[0m\u001b[0mk\u001b[0m\u001b[0;34m)\u001b[0m\u001b[0;34m,\u001b[0m\u001b[0;34m\u001b[0m\u001b[0;34m\u001b[0m\u001b[0m\n\u001b[1;32m   1208\u001b[0m                             name=k)\n\u001b[0;32m-> 1209\u001b[0;31m                     \u001b[0mret\u001b[0m\u001b[0;34m.\u001b[0m\u001b[0mappend\u001b[0m\u001b[0;34m(\u001b[0m\u001b[0mfunc\u001b[0m\u001b[0;34m(\u001b[0m\u001b[0mv\u001b[0m\u001b[0;34m)\u001b[0m\u001b[0;34m)\u001b[0m\u001b[0;34m\u001b[0m\u001b[0;34m\u001b[0m\u001b[0m\n\u001b[0m\u001b[1;32m   1210\u001b[0m         \u001b[0;32mif\u001b[0m \u001b[0mret\u001b[0m\u001b[0;34m:\u001b[0m\u001b[0;34m\u001b[0m\u001b[0;34m\u001b[0m\u001b[0m\n\u001b[1;32m   1211\u001b[0m             \u001b[0mself\u001b[0m\u001b[0;34m.\u001b[0m\u001b[0mpchanged\u001b[0m\u001b[0;34m(\u001b[0m\u001b[0;34m)\u001b[0m\u001b[0;34m\u001b[0m\u001b[0;34m\u001b[0m\u001b[0m\n", "\u001b[0;32m/usr/local/lib/python3.11/dist-packages/matplotlib/axes/_base.py\u001b[0m in \u001b[0;36mwrapper\u001b[0;34m(self, *args, **kwargs)\u001b[0m\n\u001b[1;32m     72\u001b[0m \u001b[0;34m\u001b[0m\u001b[0m\n\u001b[1;32m     73\u001b[0m         \u001b[0;32mdef\u001b[0m \u001b[0mwrapper\u001b[0m\u001b[0;34m(\u001b[0m\u001b[0mself\u001b[0m\u001b[0;34m,\u001b[0m \u001b[0;34m*\u001b[0m\u001b[0margs\u001b[0m\u001b[0;34m,\u001b[0m \u001b[0;34m**\u001b[0m\u001b[0mkwargs\u001b[0m\u001b[0;34m)\u001b[0m\u001b[0;34m:\u001b[0m\u001b[0;34m\u001b[0m\u001b[0;34m\u001b[0m\u001b[0m\n\u001b[0;32m---> 74\u001b[0;31m             \u001b[0;32mreturn\u001b[0m \u001b[0mget_method\u001b[0m\u001b[0;34m(\u001b[0m\u001b[0mself\u001b[0m\u001b[0;34m)\u001b[0m\u001b[0;34m(\u001b[0m\u001b[0;34m*\u001b[0m\u001b[0margs\u001b[0m\u001b[0;34m,\u001b[0m \u001b[0;34m**\u001b[0m\u001b[0mkwargs\u001b[0m\u001b[0;34m)\u001b[0m\u001b[0;34m\u001b[0m\u001b[0;34m\u001b[0m\u001b[0m\n\u001b[0m\u001b[1;32m     75\u001b[0m \u001b[0;34m\u001b[0m\u001b[0m\n\u001b[1;32m     76\u001b[0m         \u001b[0mwrapper\u001b[0m\u001b[0;34m.\u001b[0m\u001b[0m__module__\u001b[0m \u001b[0;34m=\u001b[0m \u001b[0mowner\u001b[0m\u001b[0;34m.\u001b[0m\u001b[0m__module__\u001b[0m\u001b[0;34m\u001b[0m\u001b[0;34m\u001b[0m\u001b[0m\n", "\u001b[0;32m/usr/local/lib/python3.11/dist-packages/matplotlib/axis.py\u001b[0m in \u001b[0;36mset_ticklabels\u001b[0;34m(self, labels, minor, fontdict, **kwargs)\u001b[0m\n\u001b[1;32m   2115\u001b[0m             \u001b[0;31m# remove all tick labels, so only error for > 0 labels\u001b[0m\u001b[0;34m\u001b[0m\u001b[0;34m\u001b[0m\u001b[0m\n\u001b[1;32m   2116\u001b[0m             \u001b[0;32mif\u001b[0m \u001b[0mlen\u001b[0m\u001b[0;34m(\u001b[0m\u001b[0mlocator\u001b[0m\u001b[0;34m.\u001b[0m\u001b[0mlocs\u001b[0m\u001b[0;34m)\u001b[0m \u001b[0;34m!=\u001b[0m \u001b[0mlen\u001b[0m\u001b[0;34m(\u001b[0m\u001b[0mlabels\u001b[0m\u001b[0;34m)\u001b[0m \u001b[0;32mand\u001b[0m \u001b[0mlen\u001b[0m\u001b[0;34m(\u001b[0m\u001b[0mlabels\u001b[0m\u001b[0;34m)\u001b[0m \u001b[0;34m!=\u001b[0m \u001b[0;36m0\u001b[0m\u001b[0;34m:\u001b[0m\u001b[0;34m\u001b[0m\u001b[0;34m\u001b[0m\u001b[0m\n\u001b[0;32m-> 2117\u001b[0;31m                 raise ValueError(\n\u001b[0m\u001b[1;32m   2118\u001b[0m                     \u001b[0;34m\"The number of FixedLocator locations\"\u001b[0m\u001b[0;34m\u001b[0m\u001b[0;34m\u001b[0m\u001b[0m\n\u001b[1;32m   2119\u001b[0m                     \u001b[0;34mf\" ({len(locator.locs)}), usually from a call to\"\u001b[0m\u001b[0;34m\u001b[0m\u001b[0;34m\u001b[0m\u001b[0m\n", "\u001b[0;31mValueError\u001b[0m: The number of FixedLocator locations (193), usually from a call to set_ticks, does not match the number of labels (517)."]}, {"output_type": "display_data", "data": {"text/plain": ["<Figure size 600x600 with 0 Axes>"]}, "metadata": {}}, {"output_type": "display_data", "data": {"text/plain": ["<Figure size 640x480 with 2 Axes>"], "image/png": "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*******************************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\n"}, "metadata": {}}]}, {"cell_type": "code", "source": ["import numpy as np\n", "import matplotlib.pyplot as plt\n", "from sklearn.model_selection import train_test_split, RandomizedSearchCV, GridSearchCV\n", "from sklearn.preprocessing import StandardScaler\n", "from sklearn.pipeline import Pipeline\n", "from sklearn.neural_network import MLPRegressor\n", "from sklearn.metrics import mean_squared_error, r2_score\n", "from scipy.stats import loguniform\n", "from sklearn.impute import SimpleImputer # Import SimpleImputer\n", "\n", "# --- 🔹 Replace with your actual data ---\n", "# Assuming X_clf_imputed (the imputed feature matrix from the classification section)\n", "# and y_clf (the target from the classification section, which is 'saleprice')\n", "# are the intended data for this MLP Regressor model.\n", "# If you want to use the data directly from the regression section before classification\n", "# then use X and y and apply imputation here.\n", "# Based on the structure and variable names, using the data prepared for classification\n", "# which was already imputed (X_clf_imputed) seems logical.\n", "\n", "all_features = X_clf_imputed # Use the imputed feature matrix\n", "target_saleprice = y_clf # Use the target variable 'saleprice'\n", "\n", "# ---  Step 1: Train/test split ---\n", "X_train, X_test, y_train, y_test = train_test_split(\n", "    all_features, target_saleprice, test_size=0.2, random_state=42\n", ")\n", "\n", "# ---  Step 2: Create pipeline ---\n", "pipeline = Pipeline([\n", "    (\"scaler\", StandardScaler()),\n", "    (\"mlp\", MLPRegressor(max_iter=500, random_state=42, early_stopping=True))\n", "])\n", "\n", "# ---  Step 3: RandomizedSearchCV ---\n", "param_dist = {\n", "    'mlp__hidden_layer_sizes': [(50,), (100,), (100, 50), (50, 25, 10)],\n", "    'mlp__alpha': loguniform(1e-4, 1e-1),\n", "    'mlp__learning_rate_init': loguniform(1e-4, 1e-1),\n", "    'mlp__activation': ['relu', 'tanh'],\n", "}\n", "\n", "random_search = RandomizedSearchCV(\n", "    estimator=pipeline,\n", "    param_distributions=param_dist,\n", "    n_iter=25,\n", "    cv=3,\n", "    scoring='neg_mean_squared_error',\n", "    n_jobs=-1,\n", "    verbose=2,\n", "    random_state=42\n", ")\n", "\n", "print(\" Running Randomized Search...\")\n", "random_search.fit(X_train, y_train)\n", "\n", "print(\"\\n Best Parameters from Random Search:\")\n", "print(random_search.best_params_)\n", "print(\"Best CV MSE:\", -random_search.best_score_)\n", "\n", "# --- 🔹 Step 4: Refined Grid Search based on Random Search results ---\n", "best_params = random_search.best_params_\n", "refined_grid = {\n", "    'mlp__hidden_layer_sizes': [best_params['mlp__hidden_layer_sizes'],\n", "                                (80, 40), (120, 60)], # Added slightly different structures\n", "    'mlp__alpha': [best_params['mlp__alpha'] / 2,\n", "                   best_params['mlp__alpha'],\n", "                   best_params['mlp__alpha'] * 2],\n", "    'mlp__learning_rate_init': [best_params['mlp__learning_rate_init'] / 2,\n", "                                best_params['mlp__learning_rate_init'],\n", "                                best_params['mlp__learning_rate_init'] * 2],\n", "    'mlp__activation': [best_params['mlp__activation']]\n", "}\n", "\n", "grid_search = GridSearchCV(\n", "    estimator=pipeline,\n", "    param_grid=refined_grid,\n", "    cv=5,\n", "    scoring='neg_mean_squared_error',\n", "    n_jobs=-1,\n", "    verbose=2\n", ")\n", "\n", "print(\"\\n Running Refined Grid Search...\")\n", "grid_search.fit(X_train, y_train)\n", "\n", "print(\"\\n Final Best Parameters:\")\n", "print(grid_search.best_params_)\n", "print(\"Refined Best CV MSE:\", -grid_search.best_score_)\n", "\n", "# --- 🔹 Step 5: Final Evaluation ---\n", "best_model = grid_search.best_estimator_\n", "\n", "y_train_pred = best_model.predict(X_train)\n", "y_test_pred = best_model.predict(X_test)\n", "\n", "train_rmse = np.sqrt(mean_squared_error(y_train, y_train_pred))\n", "test_rmse = np.sqrt(mean_squared_error(y_test, y_test_pred))\n", "train_r2 = r2_score(y_train, y_train_pred)\n", "test_r2 = r2_score(y_test, y_test_pred)\n", "\n", "print(f\"\\n Train RMSE: {train_rmse:.2f}, R²: {train_r2:.3f}\")\n", "print(f\"  Test RMSE: {test_rmse:.2f}, R²: {test_r2:.3f}\")\n", "\n", "# --- 🔹 Step 6: Plot Training Loss Curve ---\n", "# Check if the MLPRegressor has a loss_curve_ attribute (requires early_stopping=True or verbose=True)\n", "if hasattr(best_model.named_steps['mlp'], 'loss_curve_'):\n", "    plt.plot(best_model.named_steps['mlp'].loss_curve_)\n", "    plt.xlabel(\"Iteration\")\n", "    plt.ylabel(\"Training Loss\")\n", "    plt.title(\"MLP Training Loss Curve\")\n", "    plt.grid(True)\n", "    plt.tight_layout()\n", "    plt.show()\n", "else:\n", "    print(\"\\nMLPRegressor loss_curve_ not available. Set early_stopping=True or verbose=True.\")"], "metadata": {"id": "vg4Py6LETRov", "colab": {"base_uri": "https://localhost:8080/", "height": 799}, "outputId": "2c8342d4-0821-4f38-9167-c51290b6de74"}, "execution_count": 42, "outputs": [{"output_type": "stream", "name": "stdout", "text": ["🔍 Running Randomized Search...\n", "Fitting 3 folds for each of 25 candidates, totalling 75 fits\n"]}, {"output_type": "stream", "name": "stderr", "text": ["/usr/local/lib/python3.11/dist-packages/sklearn/neural_network/_multilayer_perceptron.py:691: ConvergenceWarning: Stochastic Optimizer: Maximum iterations (500) reached and the optimization hasn't converged yet.\n", "  warnings.warn(\n"]}, {"output_type": "stream", "name": "stdout", "text": ["\n", "✅ Best Parameters from Random Search:\n", "{'mlp__activation': 'relu', 'mlp__alpha': np.float64(0.024526126311336778), 'mlp__hidden_layer_sizes': (100, 50), 'mlp__learning_rate_init': np.float64(0.015702970884055395)}\n", "Best CV MSE: 2385316806.199337\n", "\n", "🔍 Running Refined Grid Search...\n", "Fitting 5 folds for each of 27 candidates, totalling 135 fits\n", "\n", "🎯 Final Best Parameters:\n", "{'mlp__activation': 'relu', 'mlp__alpha': np.float64(0.024526126311336778), 'mlp__hidden_layer_sizes': (120, 60), 'mlp__learning_rate_init': np.float64(0.03140594176811079)}\n", "Refined Best CV MSE: 1740327601.2956455\n", "\n", "📈 Train RMSE: 15523.63, R²: 0.967\n", "📉  Test RMSE: 32444.87, R²: 0.815\n"]}, {"output_type": "display_data", "data": {"text/plain": ["<Figure size 640x480 with 1 Axes>"], "image/png": "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\n"}, "metadata": {}}]}, {"cell_type": "code", "source": [], "metadata": {"id": "R5aXCWGxTR7C"}, "execution_count": null, "outputs": []}, {"cell_type": "code", "source": [], "metadata": {"id": "WE78c-g5TSMw"}, "execution_count": null, "outputs": []}, {"cell_type": "code", "source": [], "metadata": {"id": "EsfPH9Q5zw-l"}, "execution_count": null, "outputs": []}, {"cell_type": "code", "source": [], "metadata": {"id": "THFpvOGvzxHj"}, "execution_count": null, "outputs": []}]}